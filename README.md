# AI Chatbot - Intelligent Project Builder

A modern React TypeScript application for estimating custom software projects with advanced conversation intelligence, beautiful Unsplash backgrounds, and modular architecture. Features smart AI that understands when users say "all of those things" and provides comprehensive solutions instead of endless questions.

## 🚀 Key Features

### Intelligent Conversation Management
- **Smart Response Recognition**: AI detects comprehensive responses like "all of those things", "everything you mentioned", "yes to all"
- **User Frustration Detection**: Automatically switches to solution mode when users show frustration with too many questions
- **Context Preservation**: Maintains full conversation history for better understanding
- **Adaptive AI Behavior**: Dynamically adjusts questioning strategy based on user responses

### Design Integration & Analysis
- **Figma Integration**: Direct Figma URL analysis with design extraction and AI-powered recommendations
- **Image Upload Support**: Drag & drop image analysis with preview functionality using GPT-4 Vision
- **Design Intelligence**: Automatically extracts colors, typography, components, and layout structure
- **Technical Recommendations**: AI provides implementation guidance based on design complexity

### Modern React Application
- **React 18.2.0 + TypeScript 5.2.2**: Built with modern React and full type safety
- **Modular Architecture**: Clean, maintainable component structure with separation of concerns
- **Beautiful UI**: Modern glassmorphism design with CSS modules and responsive layout
- **Dynamic Backgrounds**: Unsplash integration with proper attribution and refresh functionality
- **Advanced Form Handling**: Real-time validation, character counting, and keyboard shortcuts
- **Toast Notifications**: Elegant notification system for user feedback
- **Conversation History**: Track and manage multiple conversation threads with localStorage persistence

### Enterprise Features
- **AWS Cloud Deployment**: Automated deployment scripts for both frontend (S3/CloudFront) and backend (Lambda/API Gateway)
- **API Documentation**: Auto-generated API documentation with endpoint testing
- **Connection Management**: Scripts for connecting to local and deployed environments
- **Environment Management**: Comprehensive environment variable management and validation

## 🧠 Conversation Intelligence

### Problem Solved
Traditional AI chatbots often trap users in endless question loops. When a user says "all of those things" in response to feature suggestions, most AIs continue asking more questions instead of providing comprehensive solutions.

### Our Solution
This application includes sophisticated conversation analysis that:
- **Detects Comprehensive Responses**: Recognizes when users want everything mentioned
- **Understands User Intent**: Switches from questioning mode to solution mode appropriately
- **Maintains Context**: Preserves conversation history across multiple interactions
- **Reduces Frustration**: Provides detailed solutions when users indicate comprehensive interest

### Supported Response Patterns
The AI recognizes various affirmative patterns:
- "All of those things"
- "Everything you mentioned"
- "Yes to all"
- "Include everything"
- "All the features"
- And many more comprehensive response indicators

## 🏗️ Architecture

### Frontend Structure
```
src/
├── components/           # React components
│   ├── ChatbotContainer.tsx    # Main chat interface with conversation tracking
│   ├── FormatSelector.tsx      # Project format selection
│   ├── FigmaUrlInput.tsx       # Figma URL input and validation
│   ├── ImageUpload.tsx         # Image upload with analysis
│   ├── FormSubmission.tsx      # Enhanced form submission
│   ├── ResponseDisplay.tsx     # Intelligent response display
│   ├── ResponsePopup.tsx       # Advanced popup with follow-up support
│   ├── BackgroundRefresh.tsx   # Background management
│   ├── AssumptionToggle.tsx    # Assumption mode toggle
│   ├── AssumptionOptions.tsx   # Assumption configuration
│   ├── QuoteGenerator.tsx      # Quote generation component
│   ├── RotatingCTA.tsx         # Rotating call-to-action
│   ├── Notification.tsx        # Individual notifications
│   └── NotificationContainer.tsx # Notification system
├── constants/          # Centralized constants
│   ├── messages.ts     # User-facing messages and prompts
│   ├── labels.ts       # Button labels and UI text
│   ├── placeholders.ts # Input placeholders and examples
│   ├── validation.ts   # Validation error messages
│   ├── index.ts        # Constants export and utilities
│   └── README.md       # Constants documentation
├── hooks/               # Custom React hooks
│   ├── useStateManager.ts      # Enhanced state management
│   ├── useNotification.ts      # Notification system
│   ├── useChatHistory.ts       # Conversation history tracking
│   └── useUnsplashBackground.tsx # Background management
├── services/            # API services
│   └── api.ts          # Enhanced backend communication with conversation support
├── styles/             # CSS modules with enhanced animations
│   ├── base.module.scss     # Base styles and resets
│   ├── layout.module.scss   # Layout with improved animations
│   ├── buttons.module.scss  # Button styles
│   ├── forms.module.scss    # Form and input styles
│   ├── popup.module.scss    # Popup and modal styles
│   ├── quote.module.scss    # Quote generation styles
│   └── _variables.scss      # SCSS variables with animation durations
├── types/              # TypeScript definitions
│   ├── index.ts        # Enhanced type definitions with conversation types
│   └── css-modules.d.ts # CSS modules type declarations
├── utils/              # Utility functions
│   ├── dom.ts          # DOM manipulation utilities
│   └── validation.ts   # Form validation utilities
├── App.tsx             # Main application component
└── main.tsx           # Application entry point
```

### Backend Structure
```
backend/
├── middleware/         # Express middleware
│   ├── corsMiddleware.ts       # CORS configuration
│   ├── errorMiddleware.ts      # Error handling
│   └── uploadMiddleware.ts     # File upload handling
├── prompts/            # Intelligent prompt system
│   ├── base.ts         # Base prompt templates
│   ├── generators.ts   # Dynamic prompt generation
│   └── index.ts        # Conversation analysis exports
├── routes/             # API route handlers
│   ├── chatRoutes.ts   # Chat conversation endpoints
│   ├── figmaRoutes.ts  # Figma integration endpoints
│   ├── healthRoutes.ts # Health check endpoints
│   ├── uploadRoutes.ts # Image upload endpoints
│   └── index.ts        # Route aggregation
├── services/           # Business logic services
│   ├── chatService.ts          # Core chat functionality
│   ├── conversationService.ts  # Conversation state management
│   ├── figmaService.ts         # Figma API integration
│   ├── fileService.ts          # File handling service
│   └── storageService.ts       # Data persistence
├── utils/              # Utility functions
│   └── asyncHandler.ts # Async error handling
├── server.ts           # Express server with conversation intelligence
├── chats/              # Saved conversation sessions
├── uploads/            # Temporary image storage
├── dist/               # Compiled JavaScript output
└── package.json        # Backend dependencies
```

## 🛠️ Technologies

### Frontend
- **React 18.2.0** - Modern React with hooks and concurrent features
- **TypeScript 5.2.2** - Full type safety and enhanced developer experience
- **Vite 7.0.4** - Lightning-fast build tool and development server
- **CSS Modules** - Scoped styling with improved animations
- **Unsplash API** - Dynamic background images with attribution

### Backend
- **Node.js + Express** - Server framework with TypeScript and ES modules
- **OpenAI API** - GPT-4 and GPT-4 Vision for intelligent responses and image analysis
- **Figma API** - Direct integration for design file analysis and processing
- **Advanced Conversation Analysis** - Custom logic for understanding user intent
- **Serverless Architecture** - AWS Lambda support with Serverless Express
- **Multer** - Secure file upload handling with validation
- **CORS** - Cross-origin resource sharing with custom middleware
- **Error Handling** - Comprehensive error middleware and async handling

## 🚀 Getting Started

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn
- OpenAI API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-chatbot
   ```

2. **Install dependencies**
   ```bash
   # Install frontend dependencies
   npm install
   
   # Install backend dependencies
   cd backend && npm install
   ```

3. **Environment Setup**
   
   **Frontend** (optional - for Unsplash backgrounds):
   ```env
   # .env in root directory
   VITE_UNSPLASH_ACCESS_KEY=your_unsplash_access_key_here
   ```
   
   **Backend** (required for OpenAI, optional for Figma):
   ```env
   # .env in backend directory
   OPENAI_API_KEY=your_openai_api_key_here
   FIGMA_ACCESS_TOKEN=your_figma_access_token_here  # Optional for Figma integration
   PORT=3001
   ```

   **AWS Deployment** (optional - for cloud deployment):
   ```env
   # AWS credentials for deployment scripts
   AWS_ACCESS_KEY_ID=your_aws_access_key
   AWS_SECRET_ACCESS_KEY=your_aws_secret_key
   AWS_REGION=us-east-1
   ```

4. **API Configuration**

   Configure which API the frontend should use:
   ```bash
   # For local development (recommended)
   npm run api:local        # Configure for localhost API
   npm run dev:backend      # Start backend server
   npm run dev              # Start frontend

   # For testing against deployed API
   npm run api:deployed     # Configure for AWS API
   npm run dev              # Start frontend

   # Check current configuration
   npm run api:status
   ```

   📖 **See [API Configuration Guide](docs/API-Configuration.md) for detailed documentation**

5. **Start development servers**
   ```bash
   # From root directory - starts both frontend and backend
   npm run dev

   # Or start individually
   npm run dev:frontend  # Frontend on http://localhost:5173
   npm run dev:backend   # Backend on http://localhost:3001
   ```

## 📝 Available Scripts

### Development Scripts
- `npm run dev` - Start both frontend and backend with conversation intelligence
- `npm run dev:frontend` - Start only the frontend development server
- `npm run dev:backend` - Start only the backend development server
- `npm run build` - Build the frontend for production
- `npm run preview` - Preview the production build
- `npm run type-check` - TypeScript type checking without emit
- `npm run install:backend` - Install backend dependencies

### Deployment Scripts
- `npm run deploy:frontend` - Deploy frontend to AWS S3/CloudFront
- `npm run deploy:backend` - Deploy backend to AWS Lambda/API Gateway
- `npm run deploy:full` - Deploy both frontend and backend
- `npm run deploy:frontend:update` - Update existing frontend deployment
- `npm run deploy:backend:update` - Update existing backend deployment

### Status & Management Scripts
- `npm run status:all` - Check status of all deployments
- `npm run deploy:frontend:status` - Check frontend deployment status
- `npm run deploy:backend:status` - Check backend deployment status
- `npm run docs:add` - Add API documentation to deployed backend
- `npm run docs:view` - View API documentation

### Connection Scripts
- `npm run connect:backend` - Interactive backend connection utility
- `npm run connect:local` - Connect to local backend
- `npm run connect:deployed` - Connect to deployed backend
- `npm run test:backend` - Test backend connection and functionality
- `npm run update:lambda-env` - Update Lambda environment variables

### API Configuration Scripts
- `npm run api:local` - Configure frontend to use localhost API
- `npm run api:deployed` - Configure frontend to use deployed AWS API
- `npm run api:status` - Show current API configuration
- `npm run dev:local` - Start dev server with localhost API (one-time)
- `npm run dev:deployed` - Start dev server with deployed API (one-time)

### Logging & Monitoring Scripts
- `npm run logs` - Tail Lambda logs in real-time
- `npm run logs:recent` - Show recent Lambda logs
- `npm run logs:errors` - Filter and show only ERROR logs
- `npm run logs:streams` - List available log streams

### Backend Scripts (from backend/ directory)
- `npm run dev` - Start backend with TypeScript compilation and conversation tracking
- `npm run build` - Compile TypeScript to JavaScript
- `npm start` - Start the production server

## 🎨 Enhanced Components

### ChatbotContainer
The main orchestrator with conversation intelligence:
- **Conversation Tracking**: Maintains conversation history and context
- **Smart State Management**: Handles complex conversation states
- **API Integration**: Enhanced communication with conversation-aware backend
- **User Experience**: Smooth interactions with intelligent response handling

### ResponsePopup
Advanced popup system:
- **Follow-up Support**: Allows continued conversation within popup
- **Conversation History**: Shows previous exchanges in the conversation
- **Context Preservation**: Maintains conversation thread across interactions
- **Smart Responses**: Displays comprehensive solutions when appropriate

### Enhanced API Service
Intelligent backend communication:
- **Conversation Tracking**: Sends full conversation history to backend
- **Context Preservation**: Maintains conversation state across requests
- **Figma Integration**: Direct Figma URL processing with design analysis
- **Image Analysis**: GPT-4 Vision integration for uploaded images
- **Error Handling**: Comprehensive error management with user feedback
- **Response Processing**: Handles both questioning and solution modes

## 🎯 Conversation Flow

### Traditional Flow (Problem)
1. User: "I want to build a document generator"
2. AI: "What features do you need?"
3. User: "All of those things"
4. AI: "Which specific features would you prioritize?" ❌ (Continues asking)

### Our Intelligent Flow (Solution)
1. User: "I want to build a document generator"
2. AI: "What features do you need? For example, user authentication or document templates?"
3. User: "All of those things"
4. AI: **Detects comprehensive response** ✅
5. AI: Provides detailed solution with all mentioned features, architecture recommendations, cost estimates, and implementation plan

## 🔧 Configuration

### Animation Settings
Updated for better user experience:
- **Slower Gradient Animations**: Reduced from 6s to 12s for more subtle effects
- **Enhanced Transitions**: Improved animation durations across the application
- **Reduced Motion Support**: Respects user preferences for reduced motion

### TypeScript Configuration
Strict TypeScript with enhanced types:
- **Conversation Types**: Comprehensive type definitions for conversation management
- **API Types**: Enhanced API response and request types with Figma integration
- **Component Props**: Detailed prop types for all components
- **Service Types**: Type-safe service interfaces for all integrations

### AWS Configuration
Cloud deployment ready:
- **Frontend**: S3 bucket with CloudFront CDN distribution
- **Backend**: Lambda functions with API Gateway
- **Environment Management**: Automated environment variable deployment
- **Documentation**: API Gateway documentation with testing interface

## 🚀 Deployment

### Frontend Deployment
```bash
npm run build
# Deploy the 'dist' folder to your hosting service
# Ensure environment variables are configured
```

### AWS Cloud Deployment

**Automated Deployment (Recommended)**:
```bash
# Deploy everything with one command
npm run deploy:full

# Or deploy individually
npm run deploy:frontend  # Deploy to S3/CloudFront
npm run deploy:backend   # Deploy to Lambda/API Gateway
```

**Manual Deployment**:
```bash
# Frontend to any static hosting
npm run build
# Deploy the 'dist' folder to your hosting service

# Backend to any Node.js hosting
cd backend
npm run build
npm start
# Deploy to your Node.js hosting service
```

**Environment Variables**: Ensure all required environment variables are configured in your deployment environment.

## 🧪 Testing the Intelligence

To test the conversation intelligence:

1. **Start the application**
2. **Enter a project request**: "I want to build a document generator for legal memos"
3. **Wait for AI questions**: AI will ask about specific features
4. **Respond comprehensively**: "All of those things yes" or "Everything you mentioned"
5. **Observe the magic**: AI switches to comprehensive solution mode instead of asking more questions

### Testing Figma Integration

1. **Get a Figma file URL**: Copy a public Figma file URL or ensure your file is accessible
2. **Paste the URL**: Use the Figma URL input in the interface
3. **Watch the analysis**: AI will analyze design elements, colors, typography, and provide technical recommendations
4. **Review estimates**: Get development time estimates based on design complexity

### Testing Image Analysis

1. **Upload a design mockup**: Drag and drop or click to upload an image
2. **Add description**: Provide context about what you're building
3. **Get AI analysis**: GPT-4 Vision will analyze the design and provide implementation guidance

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/conversation-enhancement`)
3. Commit your changes (`git commit -m 'Add conversation intelligence'`)
4. Push to the branch (`git push origin feature/conversation-enhancement`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **OpenAI** - For providing powerful AI models that enable intelligent conversation
- **Unsplash** - For beautiful background images
- **React Team** - For the amazing React framework
- **TypeScript Team** - For type safety and developer experience
- **Vite Team** - For the fast build tool

## 🔧 Troubleshooting

### API Connection Issues

**"Local backend is not running"**
```bash
# Check if backend is running
npm run api:status

# Start the backend server
npm run dev:backend

# Verify backend health
curl http://localhost:3001/api/health
```

**"CORS errors in browser console"**
```bash
# Check which API you're using
npm run api:status

# Switch to local API and start backend
npm run api:local
npm run dev:backend
```

**"API configuration not taking effect"**
```bash
# Check current configuration
npm run api:status

# Restart your dev server after changing configuration
# Ctrl+C to stop, then npm run dev to restart
```

### Deployment Issues

**Check deployment status:**
```bash
npm run status:all              # All deployments
npm run deploy:frontend:status  # Frontend only
npm run deploy:backend:status   # Backend only
```

**Monitor Lambda logs:**
```bash
npm run logs                    # Real-time logs
npm run logs:recent            # Recent logs
npm run logs:errors            # Error logs only
```

### Environment Issues

**Missing environment variables:**
```bash
# Check if .env files exist
ls -la .env* backend/.env*

# Copy example files
cp .env.example .env.local
cp backend/.env.example backend/.env
```

## 📞 Support

If you encounter any issues or have questions:
1. Check the [API Configuration Guide](docs/API-Configuration.md) for detailed setup instructions
2. Review the troubleshooting section above
3. Check the existing issues in the repository
4. Create a new issue with detailed information about conversation behavior
5. Include conversation examples and expected vs actual behavior
6. For deployment issues, check the deployment logs using `npm run logs`
7. For Figma integration issues, verify your access token and file permissions

---

**Built with ❤️ and 🧠 using React, TypeScript, OpenAI, Figma API, AWS Cloud Services, and intelligent conversation design**

*This application demonstrates how AI can be made more user-friendly by understanding human communication patterns, analyzing design files, and responding appropriately to comprehensive user intent.*

## 🆕 Latest Features

- **Environment-Aware API Configuration**: Automatic localhost/production API switching with manual override options
- **Real-Time Lambda Log Monitoring**: Live log tailing with filtering and error detection
- **Figma Integration**: Direct Figma file analysis with AI-powered design recommendations
- **AWS Cloud Deployment**: One-command deployment to production-ready infrastructure
- **Enhanced UI Components**: New assumption toggles, quote generators, and rotating CTAs
- **Improved API Architecture**: Modular backend with comprehensive error handling
- **Connection Management**: Scripts for seamless local/cloud environment switching
- **API Documentation**: Auto-generated documentation with interactive testing
