# API Configuration Guide

This guide explains how to configure the frontend to use different API endpoints for local development vs production.

## Quick Start

### For Local Development (Recommended)
```bash
# Configure frontend to use localhost API
npm run api:local

# Start the backend server
npm run dev:backend

# Start the frontend (in another terminal)
npm run dev
```

### For Testing Against Deployed API
```bash
# Configure frontend to use deployed AWS API
npm run api:deployed

# Start the frontend
npm run dev
```

### One-Time Development Sessions
```bash
# Use localhost API for this session only (doesn't modify .env.local)
npm run dev:local

# Use deployed API for this session only (doesn't modify .env.local)
npm run dev:deployed
```

## Available Commands

| Command | Description |
|---------|-------------|
| `npm run api:local` | Configure frontend to use localhost:3001 API |
| `npm run api:deployed` | Configure frontend to use deployed AWS API |
| `npm run api:status` | Show current API configuration |
| `npm run dev:local` | Start dev server with localhost API (one-time) |
| `npm run dev:deployed` | Start dev server with deployed API (one-time) |

## How It Works

### Automatic Environment Detection
The frontend automatically detects the environment and chooses the appropriate API:

- **Development mode** (`npm run dev`): Uses `http://localhost:3001/api` by default
- **Production build** (`npm run build`): Uses deployed AWS API Gateway

### Manual Override Options

#### 1. Persistent Configuration (Recommended)
Use the API switcher to modify `.env.local`:
```bash
npm run api:local    # Sets localhost API in .env.local
npm run api:deployed # Sets deployed API in .env.local
npm run api:status   # Check current configuration
```

#### 2. One-Time Session Override
Use environment-specific dev commands:
```bash
npm run dev:local    # Use localhost API for this session only
npm run dev:deployed # Use deployed API for this session only
```

#### 3. Manual Environment File
Edit `.env.local` directly:
```bash
# .env.local (for persistent local development)
VITE_API_BASE_URL=http://localhost:3001/api
```

## Configuration Files

### `.env.local` (Git ignored)
Your personal development configuration:
```env
VITE_API_BASE_URL=http://localhost:3001/api
```

### `.env.production`
Production build configuration:
```env
VITE_API_BASE_URL=https://5dacn8xif1.execute-api.us-east-1.amazonaws.com/prod/api
```

### `.env.example`
Template for environment variables:
```env
VITE_API_BASE_URL=http://localhost:3001/api
```

## API Endpoints

### Local Development API
- **URL**: `http://localhost:3001/api`
- **Requirements**: Backend server must be running (`npm run dev:backend`)
- **Endpoints**:
  - `POST /api/chat` - Chat messages
  - `POST /api/followup` - Follow-up questions
  - `POST /api/upload` - Image uploads
  - `GET /api/health` - Health check

### Deployed Production API
- **URL**: `https://5dacn8xif1.execute-api.us-east-1.amazonaws.com/prod/api`
- **Platform**: AWS Lambda + API Gateway
- **Same endpoints as local API**

## Troubleshooting

### "Local backend is not running"
```bash
# Start the backend server
npm run dev:backend

# Check if it's running
curl http://localhost:3001/api/health
```

### "API configuration not taking effect"
```bash
# Check current configuration
npm run api:status

# Restart your dev server after changing configuration
# Ctrl+C to stop, then npm run dev to restart
```

### "CORS errors in browser"
This usually means:
1. Backend server is not running (for local API)
2. Wrong API endpoint configured
3. Network connectivity issues (for deployed API)

```bash
# Check which API you're using
npm run api:status

# Switch to local API and start backend
npm run api:local
npm run dev:backend
```

## Development Workflows

### Full Local Development (Recommended)
```bash
# 1. Configure for local API (persistent)
npm run api:local

# 2. Start backend (terminal 1)
npm run dev:backend

# 3. Start frontend (terminal 2)
npm run dev
```

### Test Against Production API
```bash
# Option 1: Persistent configuration
npm run api:deployed     # Configure for deployed API
npm run dev              # Start frontend

# Option 2: One-time session
npm run dev:deployed     # Use deployed API for this session only
```

### Quick Development Sessions
```bash
# Local API (one-time, doesn't modify .env.local)
npm run dev:local

# Deployed API (one-time, doesn't modify .env.local)
npm run dev:deployed

# Full stack local development (both servers)
npm run dev              # Starts both frontend and backend
```

### Switching Between APIs
```bash
# Check current configuration
npm run api:status

# Switch to local development
npm run api:local

# Switch to deployed API
npm run api:deployed

# Restart dev server after switching
# Ctrl+C to stop, then npm run dev to restart
```

## Environment Variables Reference

| Variable | Description | Default |
|----------|-------------|---------|
| `VITE_API_BASE_URL` | Override API base URL | Auto-detected |
| `DEV` | Development mode flag | Set by Vite |
| `PROD` | Production mode flag | Set by Vite |
| `MODE` | Build mode | Set by Vite |

## Scripts Reference

### API Configuration Scripts
- `scripts/switch-api.sh` - Main API switcher script
- `.env.local` - Local development overrides
- `.env.production` - Production configuration

### Package.json Scripts
```json
{
  "api:local": "./scripts/switch-api.sh local",
  "api:deployed": "./scripts/switch-api.sh deployed", 
  "api:status": "./scripts/switch-api.sh status",
  "dev:local": "vite",
  "dev:deployed": "VITE_API_BASE_URL=... vite"
}
```
