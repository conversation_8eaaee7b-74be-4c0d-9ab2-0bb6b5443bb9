# Quick Reference Card

## 🚀 Common Development Commands

### Local Development Setup
```bash
npm run api:local      # Configure for localhost API
npm run dev:backend    # Start backend server
npm run dev            # Start frontend
```

### API Configuration
```bash
npm run api:local      # Use localhost:3001 API
npm run api:deployed   # Use AWS API Gateway
npm run api:status     # Check current config
```

### One-Time Sessions
```bash
npm run dev:local      # Local API (session only)
npm run dev:deployed   # Deployed API (session only)
```

### Monitoring & Logs
```bash
npm run logs           # Real-time Lambda logs
npm run logs:recent    # Recent logs
npm run logs:errors    # Error logs only
npm run status:all     # Deployment status
```

### Deployment
```bash
npm run deploy:full    # Deploy everything
npm run deploy:frontend # Frontend only
npm run deploy:backend  # Backend only
```

## 🔗 API Endpoints

| Environment | Frontend | Backend API |
|-------------|----------|-------------|
| **Local** | http://localhost:5173 | http://localhost:3001/api |
| **Deployed** | https://bloodandtreasure.com | https://5dacn8xif1.execute-api.us-east-1.amazonaws.com/prod/api |

## 🛠️ Troubleshooting Quick Fixes

| Problem | Solution |
|---------|----------|
| "Local backend not running" | `npm run dev:backend` |
| "CORS errors" | `npm run api:local && npm run dev:backend` |
| "API config not working" | `npm run api:status` then restart dev server |
| "Deployment failed" | `npm run status:all` |
| "Lambda errors" | `npm run logs:errors` |

## 📁 Key Files

| File | Purpose |
|------|---------|
| `.env.local` | Local development API config |
| `backend/.env` | Backend environment variables |
| `src/services/api.ts` | API service with environment detection |
| `scripts/switch-api.sh` | API configuration switcher |
| `docs/API-Configuration.md` | Detailed API setup guide |

## 🔄 Typical Workflows

### Full Local Development
1. `npm run api:local`
2. `npm run dev:backend` (terminal 1)
3. `npm run dev` (terminal 2)

### Test Against Production
1. `npm run api:deployed`
2. `npm run dev`

### Deploy Changes
1. `npm run deploy:full`
2. `npm run status:all`
3. `npm run logs` (monitor deployment)

### Debug Issues
1. `npm run api:status`
2. `npm run logs:errors`
3. `npm run status:all`
