import { useState, useCallback } from 'react';
import type { PopupState } from '@/types';

export const usePopupManager = () => {
  const [popupState, setPopupState] = useState<PopupState>({
    isOpen: false,
    response: null,
    imageAnalyzed: false,
    conversationId: undefined,
    followUpHistory: [],
  });

  const openPopup = useCallback((state: Omit<PopupState, 'isOpen'>) => {
    setPopupState({
      ...state,
      isOpen: true,
    });
  }, []);

  const closePopup = useCallback(() => {
    setPopupState(prev => ({ ...prev, isOpen: false }));
  }, []);

  const updatePopupState = useCallback((updates: Partial<PopupState>) => {
    setPopupState(prev => ({ ...prev, ...updates }));
  }, []);

  const openWithApiResponse = useCallback((apiResponse: any, conversation: any) => {
    openPopup({
      response: apiResponse.response,
      imageAnalyzed: apiResponse.imageAnalyzed || false,
      conversationId: conversation.id,
      followUpHistory: [],
      shouldShowEndPrompt: apiResponse.shouldShowEndPrompt || false,
      userResponseCount: apiResponse.userResponseCount || 0,
    });
  }, [openPopup]);

  const openWithConversation = useCallback((conversation: any) => {
    // Fallback for conversations with empty originalResponse
    let response = conversation.originalResponse;
    if (!response || response.trim() === '') {
      // Try to get response from follow-up history if available
      if (conversation.followUpHistory && conversation.followUpHistory.length > 0) {
        response = conversation.followUpHistory[0].response || 'Previous conversation response not available';
      } else {
        response = 'Previous conversation response not available. Please start a new conversation.';
      }
      console.log('Using fallback response:', response);
    }

    const popupData = {
      response: response,
      imageAnalyzed: conversation.imageAnalyzed,
      conversationId: conversation.id,
      followUpHistory: conversation.followUpHistory,
      shouldShowEndPrompt: false,
      userResponseCount: conversation.followUpHistory.length + 1,
    };
    openPopup(popupData);
  }, [openPopup]);

  const openWithError = useCallback((errorMessage: string, conversation: any) => {
    openPopup({
      response: errorMessage,
      imageAnalyzed: false,
      conversationId: conversation.id,
      followUpHistory: [],
      shouldShowEndPrompt: false,
      userResponseCount: 0,
    });
  }, [openPopup]);

  return {
    popupState,
    openPopup,
    closePopup,
    updatePopupState,
    openWithApiResponse,
    openWithConversation,
    openWithError,
  };
};