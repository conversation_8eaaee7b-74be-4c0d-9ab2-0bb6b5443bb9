import { useState, useCallback, useEffect } from 'react';
import type { ChatHistory, ChatHistoryState, FollowUpMessage, QuoteData } from '@/types';

// Type guards for localStorage parsing
const isValidTimestamp = (timestamp: any): timestamp is string => {
  return typeof timestamp === 'string' && !isNaN(Date.parse(timestamp));
};

const isValidFollowUp = (obj: any): obj is Omit<FollowUpMessage, 'timestamp'> & { timestamp: string } => {
  return (
    obj &&
    typeof obj.id === 'string' &&
    typeof obj.question === 'string' &&
    typeof obj.response === 'string' &&
    isValidTimestamp(obj.timestamp)
  );
};

const isValidConversation = (obj: any): obj is Omit<ChatHistory, 'timestamp' | 'followUpHistory'> & { 
  timestamp: string; 
  followUpHistory: Array<Omit<FollowUpMessage, 'timestamp'> & { timestamp: string }>;
} => {
  return (
    obj &&
    typeof obj.id === 'string' &&
    typeof obj.originalMessage === 'string' &&
    typeof obj.originalResponse === 'string' &&
    isValidTimestamp(obj.timestamp) &&
    Array.isArray(obj.followUpHistory) &&
    obj.followUpHistory.every(isValidFollowUp)
  );
};

const STORAGE_KEY = 'ai-chatbot-history';
const MAX_CONVERSATIONS = 50; // Limit stored conversations

const initialState: ChatHistoryState = {
  conversations: [],
  lastConversation: null,
};

export const useChatHistory = () => {
  const [state, setState] = useState<ChatHistoryState>(initialState);

  // Load chat history from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        
        // Validate and convert parsed data with type guards
        const conversations = Array.isArray(parsed.conversations) 
          ? parsed.conversations
              .filter(isValidConversation)
              .map((conv) => ({
                ...conv,
                timestamp: new Date(conv.timestamp),
                followUpHistory: conv.followUpHistory.map((followUp) => ({
                  ...followUp,
                  timestamp: new Date(followUp.timestamp),
                })),
              }))
          : [];
        
        const lastConversation = parsed.lastConversation && isValidConversation(parsed.lastConversation)
          ? {
              ...parsed.lastConversation,
              timestamp: new Date(parsed.lastConversation.timestamp),
              followUpHistory: parsed.lastConversation.followUpHistory.map((followUp) => ({
                ...followUp,
                timestamp: new Date(followUp.timestamp),
              })),
            }
          : null;

        setState({
          conversations,
          lastConversation,
        });
      }
    } catch (error) {
      console.warn('Failed to load chat history from localStorage:', error);
    }
  }, []);

  // Save to localStorage whenever state changes
  const saveToStorage = useCallback((newState: ChatHistoryState) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newState));
    } catch (error) {
      console.warn('Failed to save chat history to localStorage:', error);
    }
  }, []);

  // Add a new conversation
  const addConversation = useCallback((
    originalMessage: string,
    originalResponse: string,
    imageAnalyzed: boolean = false,
    conversationId?: string
  ) => {
    const newConversation: ChatHistory = {
      id: `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      originalMessage,
      originalResponse,
      imageAnalyzed,
      followUpHistory: [],
      conversationId,
    };

    setState(prevState => {
      const newConversations = [newConversation, ...prevState.conversations]
        .slice(0, MAX_CONVERSATIONS); // Keep only recent conversations
      
      const newState = {
        conversations: newConversations,
        lastConversation: newConversation,
      };
      
      saveToStorage(newState);
      return newState;
    });

    return newConversation;
  }, [saveToStorage]);

  // Add follow-up to existing conversation
  const addFollowUp = useCallback((
    conversationId: string,
    question: string,
    response: string
  ) => {
    const followUp: FollowUpMessage = {
      id: `followup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      question,
      response,
      timestamp: new Date(),
    };

    setState(prevState => {
      const updatedConversations = prevState.conversations.map(conv => 
        conv.id === conversationId 
          ? { ...conv, followUpHistory: [...conv.followUpHistory, followUp] }
          : conv
      );

      const updatedLastConversation = prevState.lastConversation?.id === conversationId
        ? { ...prevState.lastConversation, followUpHistory: [...prevState.lastConversation.followUpHistory, followUp] }
        : prevState.lastConversation;

      const newState = {
        conversations: updatedConversations,
        lastConversation: updatedLastConversation,
      };

      saveToStorage(newState);
      return newState;
    });

    return followUp;
  }, [saveToStorage]);

  // Get conversation by ID
  const getConversation = useCallback((conversationId: string): ChatHistory | null => {
    return state.conversations.find(conv => conv.id === conversationId) || null;
  }, [state.conversations]);

  // Clear all history
  const clearHistory = useCallback(() => {
    const newState = initialState;
    setState(newState);
    saveToStorage(newState);
  }, [saveToStorage]);

  // Update quote data for a conversation
  const updateQuoteData = useCallback((conversationId: string, quoteData: QuoteData) => {
    setState(prevState => {
      const updatedConversations = prevState.conversations.map(conv =>
        conv.id === conversationId
          ? { ...conv, quoteData }
          : conv
      );

      const updatedLastConversation = prevState.lastConversation?.id === conversationId
        ? { ...prevState.lastConversation, quoteData }
        : prevState.lastConversation;

      const newState = {
        conversations: updatedConversations,
        lastConversation: updatedLastConversation,
      };

      saveToStorage(newState);
      return newState;
    });
  }, [saveToStorage]);

  // Get recent conversations (for potential UI display)
  const getRecentConversations = useCallback((limit: number = 10): ChatHistory[] => {
    return state.conversations.slice(0, limit);
  }, [state.conversations]);

  return {
    conversations: state.conversations,
    lastConversation: state.lastConversation,
    addConversation,
    addFollowUp,
    getConversation,
    clearHistory,
    getRecentConversations,
    updateQuoteData,
  };
};

export default useChatHistory;
