import { useCallback } from 'react';

export const useUnsplashBackground = () => {
  const setBackgroundImage = useCallback(() => {
    document.body.style.background = 'black';
  }, []);

  const refreshBackground = useCallback(() => {
    setBackgroundImage();
  }, [setBackgroundImage]);

  return {
    refreshBackground,
    setBackgroundImage,
    isLoading: false,
    error: null,
    attributionElement: null,
  };
};

export default useUnsplashBackground;