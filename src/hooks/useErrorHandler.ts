import { useCallback } from 'react';

interface ErrorContext {
  component: string;
  action: string;
  userId?: string;
}

interface UseErrorHandlerProps {
  onNotification?: (message: string, type: 'error') => void;
}

export const useErrorHandler = ({ onNotification }: UseErrorHandlerProps = {}) => {
  const handleError = useCallback((
    error: unknown,
    context: ErrorContext,
    userMessage?: string
  ) => {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    // Log for debugging with structured data
    console.error(`[${context.component}:${context.action}]`, {
      error: errorMessage,
      context,
      timestamp: new Date().toISOString(),
      userId: context.userId,
    });

    // Show user-friendly message if notification handler is available
    if (onNotification) {
      const displayMessage = userMessage || getErrorMessage(errorMessage);
      onNotification(displayMessage, 'error');
    }
  }, [onNotification]);

  return { handleError };
};

// Helper function to convert technical errors to user-friendly messages
const getErrorMessage = (errorMessage: string): string => {
  // Network errors
  if (errorMessage.includes('fetch') || errorMessage.includes('network')) {
    return 'Network connection issue. Please check your internet connection and try again.';
  }
  
  // Server errors
  if (errorMessage.includes('500') || errorMessage.includes('Internal Server Error')) {
    return 'Server error occurred. Please try again in a few moments.';
  }
  
  // Authentication errors
  if (errorMessage.includes('401') || errorMessage.includes('unauthorized')) {
    return 'Authentication required. Please refresh the page and try again.';
  }
  
  // Validation errors (pass through as they're usually user-friendly)
  if (errorMessage.includes('validation') || errorMessage.includes('required')) {
    return errorMessage;
  }
  
  // Rate limiting
  if (errorMessage.includes('429') || errorMessage.includes('rate limit')) {
    return 'Too many requests. Please wait a moment before trying again.';
  }
  
  // File upload errors
  if (errorMessage.includes('file') && errorMessage.includes('size')) {
    return 'File is too large. Please choose a smaller image.';
  }
  
  if (errorMessage.includes('file') && errorMessage.includes('type')) {
    return 'Invalid file type. Please upload an image file.';
  }
  
  // Generic fallback
  return 'An unexpected error occurred. Please try again.';
};

// Common error contexts for consistency
export const ERROR_CONTEXTS = {
  CHAT_SUBMISSION: { component: 'ChatbotContainer', action: 'handleSubmit' },
  FIGMA_ANALYSIS: { component: 'ChatbotContainer', action: 'handleFigmaSubmit' },
  FOLLOW_UP: { component: 'ResponsePopup', action: 'handleFollowUpSubmit' },
  IMAGE_UPLOAD: { component: 'ImageUpload', action: 'handleFileSelect' },
  BACKGROUND_REFRESH: { component: 'BackgroundRefresh', action: 'refreshBackground' },
  API_REQUEST: { component: 'ApiService', action: 'request' },
} as const;