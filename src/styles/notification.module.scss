// Notification component styles
.notification {
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 300px;
  max-width: 500px;
  pointer-events: auto;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);

  &.visible {
    transform: translateY(0);
    opacity: 1;
  }

  &.hidden {
    transform: translateY(-20px);
    opacity: 0;
  }

  // Type-based background colors
  &.success {
    background: #10b981;
  }

  &.warning {
    background: #f59e0b;
  }

  &.error {
    background: #ef4444;
  }

  &.info {
    background: #3b82f6;
  }
}

.message {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

.closeButton {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: bold;
  transition: background 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}

.notificationContainer {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 8px;
  pointer-events: none;
}