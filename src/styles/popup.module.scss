@use 'variables' as *;

/* Popup Modal Styles with Dark Glass Effects */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px) var(--backdrop-saturate-high);
  -webkit-backdrop-filter: blur(8px) var(--backdrop-saturate-high);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-dropdown);
  padding: var(--spacing-xl);
  animation: fadeInGlass var(--transition-normal) ease-out;

  @include responsive('tablet') {
    padding: 15px;
  }

  @include responsive('mobile') {
    padding: var(--spacing-xs);
  }
}

.modal {
  position: relative;
  background: var(--bg-modal);
  @include glass-border();
  border-radius: var(--radius-2xl);
  box-shadow:
    var(--shadow-2xl),
    0 10px 20px rgba(0, 0, 0, 0.3),
    var(--shadow-glass);
  @include glass-effect(0, 25px);
  max-width: 700px;
  width: 100%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  animation: slideUpGlass 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  @include gradient-border(var(--gradient-rainbow), var(--radius-2xl));

  // Remove default animation - will be added conditionally
  &::before {
    animation: none;
  }

  // Animate border when asking questions (same as main element)
  &.asking::before {
    background: var(--gradient-rainbow);
    background-size: 300% 300%;
    animation: gradientShift var(--animation-normal) ease infinite;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 30% 20%,
      rgba(255, 255, 255, 0.08) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 80%,
      rgba(14, 165, 233, 0.1) 0%,
      transparent 50%
    );
    border-radius: var(--radius-2xl);
    pointer-events: none;
    z-index: 1;
  }

  @include responsive('tablet') {
    max-height: 90vh;
    border-radius: var(--radius-xl);

    &::before,
    &::after {
      border-radius: var(--radius-xl);
    }
  }

  @include responsive('mobile') {
    max-height: 95vh;
    border-radius: var(--radius-lg);

    &::before,
    &::after {
      border-radius: var(--radius-lg);
    }
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  padding-left: var(--spacing-3xl);
  border-bottom: 1px solid var(--border-primary);
  margin-bottom: 0;
  position: relative;
  z-index: 2;

  @include responsive('tablet') {
    padding: var(--spacing-2xl) var(--spacing-2xl) 0 var(--spacing-2xl);
  }

  @include responsive('mobile') {
    padding: var(--spacing-xl) var(--spacing-xl) 0 var(--spacing-xl);
  }
}

.title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  background: var(--gradient-text);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);

  @include responsive('tablet') {
    font-size: var(--font-size-xl);
  }
}

.closeButton {
  @include glass-effect(0.1);
  @include glass-border();
  font-size: var(--font-size-2xl);
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  @include glass-effect(0.1, 10px);
  position: relative;
  overflow: hidden;
  @include shimmer-effect();

  &:hover {
    @include glass-effect(0.15);
    border-color: var(--border-secondary);
    color: var(--text-primary);
    transform: scale(1.05);
  }
}

.content {
  padding: var(--spacing-3xl);
  overflow-y: auto;
  flex: 1;
  position: relative;
  z-index: 2;

  // Custom slim scrollbar styling
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    transition: background 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }

  &::-webkit-scrollbar-thumb:active {
    background: rgba(255, 255, 255, 0.6);
  }

  @include responsive('tablet') {
    padding: var(--spacing-2xl);
  }

  @include responsive('mobile') {
    padding: var(--spacing-xl);
  }
}

.imageAnalyzedBadge {
  background: rgba(3, 105, 161, 0.2);
  border: 1px solid rgba(3, 105, 161, 0.4);
  color: #60a5fa;
  padding: var(--spacing-xs) var(--spacing-base);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xl);
  display: inline-flex;
  align-items: center;
  gap: 6px;
  @include glass-effect(0, 10px);
  box-shadow: 0 4px 12px rgba(3, 105, 161, 0.2);
}

.responseContent {
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-3xl);

  // Markdown-specific styles
  :global(h1), :global(h2), :global(h3), :global(h4), :global(h5), :global(h6) {
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
    margin: var(--spacing-lg) 0 var(--spacing-md) 0;

    &:first-child {
      margin-top: 0;
    }
  }

  :global(strong) {
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
  }

  :global(em) {
    font-style: italic;
    color: var(--text-secondary);
  }

  :global(ol), :global(ul) {
    margin: var(--spacing-md) 0;
    padding-left: var(--spacing-xl);

    :global(li) {
      margin: var(--spacing-xs) 0;
      line-height: var(--line-height-normal);
    }
  }

  :global(ol) {
    list-style-type: decimal;
  }

  :global(ul) {
    list-style-type: disc;
  }

  :global(p) {
    margin: var(--spacing-md) 0;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  :global(code) {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
  }

  :global(pre) {
    background: rgba(0, 0, 0, 0.3);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    overflow-x: auto;
    margin: var(--spacing-md) 0;

    :global(code) {
      background: none;
      padding: 0;
    }
  }
}

.followUpSection {
  border-top: 1px solid var(--border-primary);
  padding-top: var(--spacing-md);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--color-secondary), transparent);
  }
}

.followUpInput {
  display: flex;
  gap: var(--spacing-lg);
  align-items: flex-end;

  @include responsive('tablet') {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
}

.followUpTextarea {
  flex: 1;
  min-height: 20px;
  padding: var(--spacing-lg);
  @include glass-effect(0.05);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-family: inherit;
  color: var(--text-secondary);
  resize: vertical;
  transition: var(--transition-normal);
  @include glass-effect(0.05, 10px);

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }

  &:focus {
    outline: none;
    border-color: var(--color-secondary);
    box-shadow: 
      0 0 0 4px rgba(131, 56, 236, 0.2),
      0 4px 12px rgba(131, 56, 236, 0.3);
    @include glass-effect(0.08);
  }

  &:disabled {
    background: rgba(255, 255, 255, 0.02);
    color: var(--text-disabled);
    cursor: not-allowed;
  }
}

.sendButton {
  position: relative;
  background: var(--gradient-secondary);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: var(--transition-normal);
  white-space: nowrap;
  height: fit-content;
  box-shadow: 0 4px 12px rgba(131, 56, 236, 0.3);
  overflow: hidden;
  @include shimmer-effect();
  // Remove gradient border - no border on send button

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--color-secondary-light), var(--color-accent-blue-dark));
    @include hover-lift();
    box-shadow: 0 8px 20px rgba(131, 56, 236, 0.4);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    @include glass-effect(0.1);
    color: var(--text-disabled);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  @include responsive('tablet') {
    width: 100%;
    justify-content: center;
  }
}

// Remove sendButtonLoading styles - no border animation on button

.loadingSpinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  margin-right: var(--spacing-sm);
}

.errorMessage {
  @include glass-effect(0, 10px);
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid rgba(220, 38, 38, 0.3);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  color: #fca5a5;
  margin-bottom: var(--spacing-xl);
}

/* Rotating CTA Section Styles */
.rotatingCtaSection {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md) 0;
  border-top: 1px solid rgba(34, 197, 94, 0.2);
  border-bottom: 1px solid rgba(34, 197, 94, 0.2);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.6), transparent);
  }

  // &::after {
  //   content: '';
  //   position: absolute;
  //   bottom: 0;
  //   left: 50%;
  //   transform: translateX(-50%);
  //   width: 60px;
  //   height: 1px;
  //   background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.6), transparent);
  // }
}

.rotatingCtaCard {
  @include glass-effect(0.05);
  @include glass-border();
  border-radius: var(--radius-xl);
  padding: var(--spacing-md);
  background: linear-gradient(135deg,
    rgba(34, 197, 94, 0.08) 0%,
    rgba(16, 185, 129, 0.06) 50%,
    rgba(6, 182, 212, 0.04) 100%
  );
  border: 1px solid rgba(34, 197, 94, 0.3);
  box-shadow:
    0 4px 16px rgba(34, 197, 94, 0.1),
    0 2px 8px rgba(16, 185, 129, 0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 30% 20%,
      rgba(34, 197, 94, 0.1) 0%,
      transparent 60%
    );
    pointer-events: none;
  }
}

.rotatingCtaContent {
  display: flex;
  flex-direction: row;
  align-items: center;
  text-align: left;
  gap: var(--spacing-md);
  position: relative;
  z-index: 1;
  justify-content: space-between;
}

.rotatingCtaIcon {
  width: 40px;
  color: #22c55e;
  opacity: 0.8;
}

.rotatingCtaText {
  font-style: italic;
  color: #22c55e;
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  margin: 0;
  font-weight: var(--font-weight-medium);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  @include responsive('tablet') {
    font-size: var(--font-size-base);
  }
}

.rotatingCtaButton {
  position: relative;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: var(--transition-normal);
  box-shadow:
    0 4px 12px rgba(34, 197, 94, 0.3),
    0 2px 6px rgba(16, 163, 74, 0.2);
  overflow: hidden;
  @include shimmer-effect();
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);

  &:hover {
    background: linear-gradient(135deg, #16a34a, #15803d);
    transform: translateY(-2px);
    box-shadow:
      0 6px 16px rgba(34, 197, 94, 0.4),
      0 3px 8px rgba(16, 163, 74, 0.3);
  }

  &:active {
    transform: translateY(0);
  }

  @include responsive('tablet') {
    width: 100%;
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--font-size-sm);
  }
}



/* Animations */
@keyframes fadeInGlass {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

@keyframes slideUpGlass {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.9);
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    backdrop-filter: blur(25px);
  }
}



@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 200% 50%; }
  75% { background-position: 300% 50%; }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .modal {
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(0, 0, 0, 0.95);
  }
  
  .closeButton {
    border-color: rgba(255, 255, 255, 0.5);
  }
  
  .followUpTextarea {
    border-color: var(--border-secondary);
  }
  
  .sendButton {
    border: 2px solid transparent;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .modal::before,
  .overlay,
  .modal,
  .closeButton,
  .sendButton,
  .closeButton::before,
  .sendButton::before,
  .sendButtonLoading::before,
  .loadingSpinner {
    animation: none;
    transition: none;
  }

  .closeButton::before,
  .sendButton::before,
  .sendButtonLoading::before {
    display: none;
  }
}

/* Figma Integration Styles */
.figmaSection {
  margin: var(--spacing-xl) 0;
  padding: var(--spacing-xl);
  background: rgba(139, 92, 246, 0.05);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: var(--radius-lg);
}

.figmaHeader {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  color: var(--color-secondary);
  
  h4 {
    margin: 0;
    font-size: var(--font-size-md);
    font-weight: 600;
  }
}

.figmaIcon {
  color: rgba(139, 92, 246, 0.8);
}

.figmaForm {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.figmaInputContainer {
  position: relative;
}

.inputWithIcon {
  position: relative;
  display: flex;
  align-items: center;
}

.linkIcon {
  position: absolute;
  left: var(--spacing-md);
  color: rgba(139, 92, 246, 0.6);
  z-index: 2;
}

.figmaUrlInput {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) calc(var(--spacing-md) * 3);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: var(--radius-md);
  background: rgba(139, 92, 246, 0.1);
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  transition: all 0.2s ease;
  resize: none;
  height: auto;
  min-height: 44px;
  
  &:focus {
    outline: none;
    border-color: rgba(139, 92, 246, 0.5);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
  }
  
  &.checking {
    border-color: rgba(234, 179, 8, 0.5);
    background: rgba(234, 179, 8, 0.1);
  }
  
  &.valid {
    border-color: rgba(34, 197, 94, 0.5);
    background: rgba(34, 197, 94, 0.1);
  }
  
  &.invalid {
    border-color: rgba(239, 68, 68, 0.5);
    background: rgba(239, 68, 68, 0.1);
  }
}

.validationIcon {
  position: absolute;
  right: var(--spacing-md);
  display: flex;
  align-items: center;
  height: 100%;
}

.validIcon {
  color: rgba(34, 197, 94, 0.8);
}

.invalidIcon {
  color: rgba(239, 68, 68, 0.8);
}

.spinner {
  color: rgba(234, 179, 8, 0.8);
  animation: spin 1s linear infinite;
}

.validationError {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: var(--radius-sm);
  color: rgba(239, 68, 68, 0.9);
  font-size: var(--font-size-xs);
}

.figmaBtn {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.8), rgba(99, 102, 241, 0.8));
  border: 1px solid rgba(139, 92, 246, 0.3);
  color: white;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-md);
  font-weight: 600;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    background: linear-gradient(135deg, rgba(139, 92, 246, 1), rgba(99, 102, 241, 1));
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

.figmaHint {
  margin-top: var(--spacing-md);
  
  p {
    margin: 0;
    color: rgba(139, 92, 246, 0.7);
    font-size: var(--font-size-xs);
    line-height: 1.4;
  }
}

// Typing cursor animation for streaming responses
:global(.typing-cursor) {
  display: inline-block;
  animation: blink 1s infinite;
  color: #007bff;
  font-weight: bold;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}
