import type { ApiResponse, FormData, FollowUpData, StreamingCallbacks } from '@/types';

// Environment-based API configuration
const getApiBaseUrl = (): string => {
  // Check if we're in development mode
  const isDevelopment = import.meta.env.DEV;

  // Allow override via environment variable
  const envApiUrl = import.meta.env.VITE_API_BASE_URL;
  if (envApiUrl) {
    return envApiUrl;
  }

  // Default URLs based on environment
  if (isDevelopment) {
    return 'http://localhost:3001/api';
  } else {
    return 'https://5dacn8xif1.execute-api.us-east-1.amazonaws.com/prod/api';
  }
};

const API_BASE_URL = getApiBaseUrl();

// Log the API configuration on startup
console.log('🔗 API Configuration:', {
  baseUrl: API_BASE_URL,
  isDevelopment: import.meta.env.DEV,
  mode: import.meta.env.MODE
});

export class ApiService {
  private static instance: ApiService;

  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  private constructor() {}

  private async handleResponse(response: Response, operation: string = 'API'): Promise<any> {
    if (!response.ok) {
      console.error(`❌ ${operation}: Response not OK, attempting to parse error`);
      const errorData = await response.json().catch(() => ({}));
      console.error(`❌ ${operation}: Error data:`, errorData);
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  async sendMessage(formData: FormData, conversationHistory?: any[]): Promise<ApiResponse> {
    console.log('API: Starting sendMessage request');
    console.log('API: Form data:', {
      messageLength: formData.message.length,
      hasImage: !!formData.selectedImage,
      format: formData.selectedFormat,
      imageSize: formData.selectedImage ? `${(formData.selectedImage.size / 1024).toFixed(2)}KB` : 'N/A',
      historyLength: conversationHistory?.length || 0
    });

    try {
      let response: Response;

      if (formData.selectedImage) {
        console.log('API: Handling image upload request');
        // Handle image upload
        const uploadData = new FormData();
        uploadData.append('image', formData.selectedImage);
        uploadData.append('message', formData.message);
        uploadData.append('format', formData.selectedFormat);

        console.log('API: Sending POST request to /upload endpoint');
        response = await fetch(`${API_BASE_URL}/upload`, {
          method: 'POST',
          body: uploadData,
        });
      } else {
        console.log('API: Handling text-only chat request');
        // Build conversation messages including history
        const messages = [
          ...(conversationHistory || []),
          { role: 'user', content: formData.message }
        ];
        
        const requestBody = {
          messages,
          format: formData.selectedFormat,
          conversationId: formData.conversationId,
          assumptionMode: formData.assumptionMode
        };
        console.log('API: Sending POST request to /chat endpoint with body:', {
          ...requestBody,
          messages: `${messages.length} messages`
        });
        
        response = await fetch(`${API_BASE_URL}/chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        });
      }

      console.log('API: Response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });

      const data = await this.handleResponse(response, 'API');
      console.log('✅ API: Success response data:', {
        responseLength: data.response?.length || 0,
        imageAnalyzed: data.imageAnalyzed,
        conversationId: data.conversationId
      });

      const result = {
        response: data.response,
        imageAnalyzed: data.imageAnalyzed || false,
        conversationId: data.conversationId || this.generateConversationId(),
        shouldShowEndPrompt: data.shouldShowEndPrompt || false,
        userResponseCount: data.userResponseCount || 0,
      };

      console.log('API: sendMessage completed successfully');
      return result;
    } catch (error) {
      console.error('API: sendMessage error occurred:', error);
      console.error('API: Error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });
      
      if (error instanceof Error) {
        throw new Error(error.message);
      }
      
      throw new Error('An unexpected error occurred');
    }
  }

  async sendFollowUp(followUpData: FollowUpData, conversationHistory?: any[]): Promise<ApiResponse> {
    console.log('API: Starting sendFollowUp request');
    console.log('API: Follow-up data:', {
      messageLength: followUpData.message.length,
      conversationId: followUpData.conversationId,
      historyLength: conversationHistory?.length || 0
    });

    try {
      // Build conversation messages including history
      const messages = [
        ...(conversationHistory || []),
        { role: 'user', content: followUpData.message }
      ];
      
      const requestBody = {
        messages,
        conversationId: followUpData.conversationId,
        assumptionMode: followUpData.assumptionMode,
      };
      console.log('API: Sending POST request to /followup endpoint with body:', {
        ...requestBody,
        messages: `${messages.length} messages`
      });

      const response = await fetch(`${API_BASE_URL}/followup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      console.log('API: Follow-up response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });

      const data = await this.handleResponse(response, 'API Follow-up');
      console.log('✅ API: Follow-up success response data:', {
        responseLength: data.response?.length || 0,
        conversationId: data.conversationId
      });

      const result = {
        response: data.response,
        imageAnalyzed: false,
        conversationId: data.conversationId || followUpData.conversationId,
        shouldShowEndPrompt: data.shouldShowEndPrompt || false,
        userResponseCount: data.userResponseCount || 0,
      };

      console.log('API: sendFollowUp completed successfully');
      return result;
    } catch (error) {
      console.error('API: sendFollowUp error occurred:', error);
      console.error('API: Follow-up error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });
      
      if (error instanceof Error) {
        throw new Error(error.message);
      }
      
      throw new Error('An unexpected error occurred');
    }
  }

  private generateConversationId(): string {
    const id = `conv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    console.log('🆔 API: Generated new conversation ID:', id);
    return id;
  }

  async healthCheck(): Promise<boolean> {
    console.log('API: Starting health check');
    
    try {
      console.log('API: Sending GET request to /health endpoint');
      const response = await fetch(`${API_BASE_URL}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      console.log('API: Health check response:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });
      
      const isHealthy = response.ok;
      console.log(`${isHealthy ? '✅' : '❌'} API: Health check result: ${isHealthy ? 'HEALTHY' : 'UNHEALTHY'}`);
      
      return isHealthy;
    } catch (error) {
      console.warn('API: Health check failed with error:', error);
      console.warn('API: Health check error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  async sendMessageStream(formData: FormData, conversationHistory: any[], callbacks: StreamingCallbacks): Promise<void> {
    console.log('API: Starting streaming sendMessage request');

    if (formData.selectedImage) {
      // For now, fall back to regular API for image uploads
      console.log('API: Image uploads not yet supported in streaming mode, falling back to regular API');
      const response = await this.sendMessage(formData, conversationHistory);
      callbacks.onMetadata({
        conversationId: response.conversationId || 'unknown',
        shouldShowEndPrompt: response.shouldShowEndPrompt || false,
        userResponseCount: response.userResponseCount || 0
      });
      callbacks.onContent(response.response, response.response.length);
      callbacks.onComplete({
        shouldAutoGenerateQuote: response.shouldAutoGenerateQuote || false,
        quoteData: response.quoteData,
        totalTokens: response.response.length
      });
      return;
    }

    try {
      // Build conversation messages including history
      const messages = [
        ...(conversationHistory || []),
        { role: 'user', content: formData.message }
      ];

      const requestBody = {
        messages,
        format: formData.selectedFormat,
        conversationId: formData.conversationId,
        assumptionMode: formData.assumptionMode
      };

      console.log('API: Starting streaming fetch to /chat/stream');

      const response = await fetch(`${API_BASE_URL}/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        buffer += decoder.decode(value, { stream: true });

        // Process complete lines
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6)); // Remove 'data: ' prefix

              switch (data.type) {
                case 'metadata':
                  callbacks.onMetadata(data);
                  break;
                case 'content':
                  callbacks.onContent(data.content, data.tokenCount);
                  break;
                case 'complete':
                  callbacks.onComplete(data);
                  return; // Exit the function
                case 'error':
                  callbacks.onError(data.error, data.details);
                  return; // Exit the function
              }
            } catch (error) {
              console.error('API: Error parsing streaming data:', error);
            }
          }
        }
      }

    } catch (error: unknown) {
      console.error('API: Streaming request failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown streaming error';
      callbacks.onError('Failed to start streaming request', errorMessage);
    }
  }

  async sendFollowUpStream(followUpData: FollowUpData, conversationHistory: any[], callbacks: StreamingCallbacks): Promise<void> {
    console.log('API: Starting streaming follow-up request');

    try {
      // Build conversation messages including history
      const messages = [
        ...(conversationHistory || []),
        { role: 'user', content: followUpData.message }
      ];

      const requestBody = {
        messages,
        conversationId: followUpData.conversationId,
        assumptionMode: followUpData.assumptionMode,
      };

      console.log('API: Starting streaming fetch to /followup/stream');

      const response = await fetch(`${API_BASE_URL}/followup/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        buffer += decoder.decode(value, { stream: true });

        // Process complete lines
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6)); // Remove 'data: ' prefix

              switch (data.type) {
                case 'metadata':
                  callbacks.onMetadata(data);
                  break;
                case 'content':
                  callbacks.onContent(data.content, data.tokenCount);
                  break;
                case 'complete':
                  callbacks.onComplete(data);
                  return; // Exit the function
                case 'error':
                  callbacks.onError(data.error, data.details);
                  return; // Exit the function
              }
            } catch (error) {
              console.error('API: Error parsing streaming data:', error);
            }
          }
        }
      }

    } catch (error: unknown) {
      console.error('API: Streaming follow-up request failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown streaming error';
      callbacks.onError('Failed to start streaming follow-up request', errorMessage);
    }
  }

  isOnline(): boolean {
    const online = navigator.onLine;
    console.log(`API: Network status check - ${online ? 'ONLINE' : 'OFFLINE'}`);
    return online;
  }
}

// Export singleton instance
export const apiService = ApiService.getInstance();
export default apiService;
