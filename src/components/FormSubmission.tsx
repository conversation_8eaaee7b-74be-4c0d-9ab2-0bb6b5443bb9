import React from 'react';
import { LABELS } from '@/constants';
import type { LoadableComponentProps } from '@/types';
import buttonStyles from '@/styles/buttons.module.scss';

interface FormSubmissionProps extends LoadableComponentProps {
  onSubmit: () => void;
}

const FormSubmission: React.FC<FormSubmissionProps> = ({
  onSubmit,
  isLoading,
  disabled = false,
  className,
}) => {
  const handleClick = () => {
    if (!disabled && !isLoading) {
      onSubmit();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  };

  return (
    <button
      type="button"
      className={`${buttonStyles.btnPrimary} ${className || ''}`}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={disabled || isLoading}
      aria-label={isLoading ? LABELS.ARIA_SUBMIT_LOADING : LABELS.ARIA_SUBMIT_DEFAULT}
    >
      {isLoading ? LABELS.SENDING : LABELS.SUBMIT}
    </button>
  );
};

export { FormSubmission };
export default FormSubmission;
