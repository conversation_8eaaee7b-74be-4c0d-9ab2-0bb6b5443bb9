import React, { useEffect, useRef } from 'react';
import { ImageIcon } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import layoutStyles from '@/styles/layout.module.scss';
import responseDisplayStyles from '@/styles/response-display.module.scss';

interface ResponseDisplayProps {
  response: string;
  imageAnalyzed?: boolean;
  className?: string;
  isStreaming?: boolean;
  showTypingCursor?: boolean;
}

const ResponseDisplay: React.FC<ResponseDisplayProps> = ({
  response,
  imageAnalyzed = false,
  className,
  isStreaming = false,
  showTypingCursor = false,
}) => {
  const responseRef = useRef<HTMLDivElement>(null);

  // Scroll into view when response appears
  useEffect(() => {
    if (responseRef.current) {
      responseRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [response]);

  const formatResponseForMarkdown = (text: string) => {
    let formattedText = text;

    // Handle Q: and Assume: patterns for markdown
    formattedText = formattedText.replace(
      /Q:\s*([^\n]*)/gi,
      '**Q:** $1'
    );

    formattedText = formattedText.replace(
      /Assume:\s*([^\n]*)/gi,
      '• $1'
    );

    return formattedText;
  };

  const isError = response.includes('Error:');

  return (
    <div 
      ref={responseRef}
      className={`${layoutStyles.responseCard} ${layoutStyles.visible} ${className || ''}`}
      role="region"
      aria-label="Quote Assessment"
    >
      <h3 className={layoutStyles.responseHeader}>AI Analysis</h3>
      
      {imageAnalyzed && (
        <div className={layoutStyles.imageAnalyzedBadge}>
          <ImageIcon size={16} className={responseDisplayStyles.iconMargin} />
          Image analyzed
        </div>
      )}
      
      <div className={isError ? layoutStyles.errorMessage : layoutStyles.responseContent}>
        <ReactMarkdown>{formatResponseForMarkdown(response)}</ReactMarkdown>
        {showTypingCursor && isStreaming && <span className="typing-cursor">|</span>}
      </div>
    </div>
  );
};

export { ResponseDisplay };
export default ResponseDisplay;
