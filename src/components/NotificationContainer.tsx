import React from 'react';
import { Notification } from './Notification';
import styles from '@/styles/notification.module.scss';

interface NotificationState {
  id: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  visible: boolean;
}

interface NotificationContainerProps {
  notifications: NotificationState[];
  onClose: (id: string) => void;
  className?: string;
}

const NotificationContainer: React.FC<NotificationContainerProps> = ({
  notifications,
  onClose,
  className,
}) => {
  if (notifications.length === 0) {
    return null;
  }

  return (
    <div 
      className={`${styles.notificationContainer} ${className || ''}`}
      role="region"
      aria-label="Notifications"
    >
      {notifications.map((notification) => (
        <Notification
          key={notification.id}
          message={notification.message}
          type={notification.type}
          visible={notification.visible}
          onClose={() => onClose(notification.id)}
        />
      ))}
    </div>
  );
};

export { NotificationContainer };
export default NotificationContainer;
