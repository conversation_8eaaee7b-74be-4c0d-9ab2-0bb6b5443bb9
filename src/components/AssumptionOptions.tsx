import React, { useMemo } from 'react';
import { Lightbulb } from 'lucide-react';
import buttonStyles from '@/styles/buttons.module.scss';
import layoutStyles from '@/styles/layout.module.scss';
import type { FollowUpMessage } from '@/types';

interface AssumptionOptionsProps {
  response: string;
  followUpHistory?: FollowUpMessage[];
  onAssumptionSelect: (assumption: string) => void;
  disabled?: boolean;
}

const AssumptionOptions: React.FC<AssumptionOptionsProps> = ({
  response,
  followUpHistory = [],
  onAssumptionSelect,
  disabled = false,
}) => {
  // Memoize expensive assumption parsing
  const assumptions = useMemo(() => {
    // Get the latest response text to parse for assumptions
    const latestResponseText = followUpHistory.length > 0
      ? followUpHistory[followUpHistory.length - 1].response
      : response;

    // Parse the response to extract only Assume: statements
    const assumptions: string[] = [];
    const lines = latestResponseText.split('\n').map(line => line.trim()).filter(line => line);

    for (const line of lines) {
      if (line.startsWith('Assume:')) {
        assumptions.push(line.substring(7).trim());
      }
    }

    return assumptions;
  }, [response, followUpHistory]);

  // Debug log to verify the fix is working
  if (process.env.NODE_ENV === 'development') {
    console.log('AssumptionOptions: Follow-up history length:', followUpHistory.length);
    console.log('AssumptionOptions: Raw response:', response);
    if (followUpHistory.length > 0) {
      console.log('AssumptionOptions: Latest follow-up response:', followUpHistory[followUpHistory.length - 1].response);
    }
    console.log('AssumptionOptions: Parsed assumptions:', assumptions);
  }

  // If no assumptions found, don't render anything
  if (assumptions.length === 0) {
    return null;
  }

  return (
    <div className={layoutStyles.assumptionOptionsContainer}>
      <div className={layoutStyles.assumptionOptionsHeader}>
        <Lightbulb size={16} />
        <span>Click below to select an assumption</span>
      </div>

      {assumptions.map((assumption, index) => (
        <button
          key={index}
          className={`${buttonStyles.btn} ${buttonStyles.assumptionButton}`}
          onClick={() => onAssumptionSelect(assumption)}
          disabled={disabled}
          type="button"
        >
          <span>{assumption}</span>
        </button>
      ))}
      
      {/* <div className={layoutStyles.assumptionOptionsFooter}>
        Or answer the questions manually in the text box below
      </div> */}
    </div>
  );
};

export { AssumptionOptions };
export default AssumptionOptions;
