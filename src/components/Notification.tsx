import React from 'react';
import styles from '@/styles/notification.module.scss';

interface NotificationProps {
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  visible: boolean;
  onClose: () => void;
  className?: string;
}

const Notification: React.FC<NotificationProps> = ({
  message,
  type,
  visible,
  onClose,
  className,
}) => {
  const getIcon = () => {
    switch (type) {
      case 'success':
        return '✅';
      case 'warning':
        return '⚠️';
      case 'error':
        return '❌';
      default:
        return 'ℹ️';
    }
  };


  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <div
      className={`${styles.notification} ${styles[type]} ${visible ? styles.visible : styles.hidden} ${className || ''}`}
      role="alert"
      aria-live="polite"
      tabIndex={0}
      onKeyDown={handleKeyDown}
    >
      <span aria-hidden="true">{getIcon()}</span>
      <span className={styles.message}>
        {message}
      </span>
      <button
        type="button"
        onClick={onClose}
        className={styles.closeButton}
        aria-label="Close notification"
      >
        ✕
      </button>
    </div>
  );
};

export { Notification };
export default Notification;
