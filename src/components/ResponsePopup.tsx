import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Bot, ImageIcon } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { apiService } from '@/services/api';
import { MESSAGES, LABELS, PLACEHOLDERS, VALIDATION_MESSAGES } from '@/constants';
import { AssumptionOptions } from './AssumptionOptions';
import { RotatingCTA } from './RotatingCTA';
import { QuoteGenerator } from './QuoteGenerator';
import type { PopupState, FollowUpData, FollowUpMessage, QuoteData } from '@/types';
import popupStyles from '@/styles/popup.module.scss';
import responsePopupStyles from '@/styles/response-popup.module.scss';

interface ResponsePopupProps {
  popupState: PopupState;
  onClose: () => void;
  onNotification: (message: string, type: 'info' | 'success' | 'warning' | 'error') => void;
  onFollowUpAdded: (conversationId: string, question: string, response: string, shouldShowEndPrompt?: boolean, userResponseCount?: number) => void;
  projectDescription?: string;
  projectFormat?: string;
  assumptionMode?: boolean;
  autoQuoteData?: QuoteData;
  onRequestNewQuote?: () => void;
  onQuoteGenerated?: (quote: QuoteData) => void;
  onAskAgain?: () => void;
  isStreaming?: boolean;
  streamingResponse?: string;
  originalQuestion?: string;
}

const ResponsePopup: React.FC<ResponsePopupProps> = ({
  popupState,
  onClose,
  onNotification,
  onFollowUpAdded,
  projectDescription = '',
  projectFormat = 'web',
  assumptionMode = false,
  autoQuoteData,
  onRequestNewQuote,
  onQuoteGenerated,
  onAskAgain,
  isStreaming = false,
  streamingResponse = '',
  originalQuestion,
}) => {
  const [followUpMessage, setFollowUpMessage] = useState('');
  const [isLoadingFollowUp, setIsLoadingFollowUp] = useState(false);
  const [followUpHistory, setFollowUpHistory] = useState<FollowUpMessage[]>(popupState.followUpHistory);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const [buttonState, setButtonState] = useState<'send' | 'sending' | 'receiving'>('send');
  const overlayRef = useRef<HTMLDivElement>(null);
  const followUpTextareaRef = useRef<HTMLTextAreaElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Scroll to top of popup content
  const scrollToTop = useCallback(() => {
    if (contentRef.current) {
      contentRef.current.scrollTop = 0;
      // Force a second scroll attempt in case content is still loading
      setTimeout(() => {
        if (contentRef.current) {
          contentRef.current.scrollTop = 0;
        }
      }, 50);
    }
  }, []);

  // Handle escape key to close popup
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (popupState.isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when popup is open
      document.body.style.overflow = 'hidden';

      // Scroll to top when popup opens - delay to ensure it happens after focus
      setTimeout(() => {
        scrollToTop();
      }, 400);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [popupState.isOpen, onClose, scrollToTop]);

  // Scroll to top when follow-up history changes (new Q&A added)
  useEffect(() => {
    if (followUpHistory.length > 0) {
      setTimeout(() => {
        scrollToTop();
      }, 200);
    }
  }, [followUpHistory.length, scrollToTop]);

  // Handle click outside to close popup
  const handleOverlayClick = useCallback((event: React.MouseEvent) => {
    if (event.target === overlayRef.current) {
      onClose();
    }
  }, [onClose]);

  // Handle assumption selection - auto-send like typing and submitting
  const handleAssumptionSelect = useCallback(async (assumption: string) => {
    if (isLoadingFollowUp) return;

    // Mark that user has interacted
    setHasUserInteracted(true);

    const followUpData: FollowUpData = {
      message: assumption,
      conversationId: popupState.conversationId,
      assumptionMode: true, // Force assumption mode for this request
    };

    setIsLoadingFollowUp(true);
    setButtonState('sending');

    try {
      const response = await apiService.sendFollowUp(followUpData);

      // Switch to receiving state when we get the response
      setButtonState('receiving');

      // Small delay to show "Receiving..." state
      await new Promise(resolve => setTimeout(resolve, 800));

      const newFollowUp: FollowUpMessage = {
        id: Date.now().toString(),
        question: assumption, // Use the assumption directly as the question
        response: response.response,
        timestamp: new Date(),
      };

      setFollowUpHistory(prev => [...prev, newFollowUp]);

      // Notify parent component to update chat history
      onFollowUpAdded(popupState.conversationId!, assumption, response.response, response.shouldShowEndPrompt, response.userResponseCount);

      // Handle quote generation if present
      if (response.quoteData && onQuoteGenerated) {
        onQuoteGenerated(response.quoteData);
      }

      // Scroll to top after receiving response
      setTimeout(() => {
        scrollToTop();
      }, 200);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : VALIDATION_MESSAGES.FOLLOWUP_SEND_FAILED;
      onNotification(errorMessage, 'error');
    } finally {
      setIsLoadingFollowUp(false);
      setButtonState('send');
    }
  }, [isLoadingFollowUp, popupState.conversationId, onNotification, onFollowUpAdded, scrollToTop]);

  // Handle follow-up submission
  const handleFollowUpSubmit = useCallback(async () => {
    if (!followUpMessage.trim() || isLoadingFollowUp) return;

    // Mark that user has interacted
    setHasUserInteracted(true);

    const followUpData: FollowUpData = {
      message: followUpMessage.trim(),
      conversationId: popupState.conversationId,
      assumptionMode: assumptionMode,
    };

    setIsLoadingFollowUp(true);
    setButtonState('sending');

    try {
      // Use the dedicated follow-up API endpoint
      const response = await apiService.sendFollowUp(followUpData);

      // Switch to receiving state when we get the response
      setButtonState('receiving');

      // Small delay to show "Receiving..." state
      await new Promise(resolve => setTimeout(resolve, 800));

      const newFollowUp: FollowUpMessage = {
        id: Date.now().toString(),
        question: followUpMessage.trim(),
        response: response.response,
        timestamp: new Date(),
      };

      setFollowUpHistory(prev => [...prev, newFollowUp]);
      setFollowUpMessage('');

      // Notify parent component to update chat history
      onFollowUpAdded(popupState.conversationId!, followUpMessage.trim(), response.response, response.shouldShowEndPrompt, response.userResponseCount);

      // Handle quote generation if present
      if (response.quoteData && onQuoteGenerated) {
        onQuoteGenerated(response.quoteData);
      }

      // Scroll to top after receiving response
      setTimeout(() => {
        scrollToTop();
      }, 200);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : VALIDATION_MESSAGES.FOLLOWUP_SEND_FAILED;
      onNotification(errorMessage, 'error');
    } finally {
      setIsLoadingFollowUp(false);
      setButtonState('send');
    }
  }, [followUpMessage, isLoadingFollowUp, popupState.conversationId, onNotification, onFollowUpAdded, assumptionMode, scrollToTop]);

  // Handle Enter key in textarea
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleFollowUpSubmit();
    }
  }, [handleFollowUpSubmit]);

  // Sync followUpHistory when popupState changes
  useEffect(() => {
    setFollowUpHistory(popupState.followUpHistory);
  }, [popupState.followUpHistory]);

  // Reset interaction state when popup opens with new conversation
  useEffect(() => {
    if (popupState.isOpen && !autoQuoteData) {
      setHasUserInteracted(false);
    }
  }, [popupState.isOpen, popupState.conversationId, autoQuoteData]);

  // Get the latest response text to display (same logic as AssumptionOptions)
  const getLatestResponseText = (): string => {
    // If there are follow-up responses, use the latest one
    if (followUpHistory.length > 0) {
      const latestFollowUp = followUpHistory[followUpHistory.length - 1];
      return latestFollowUp.response.toString().replace(/Assume:.+/g, '').replace(/Q: /, '')
    }

    // If streaming, use streaming response
    if (isStreaming && streamingResponse) {
      return streamingResponse.replace(/Assume:.+/g, '').replace(/Q: /, '');
    }

    // Otherwise, use the original response
    return popupState.response?.toString().replace(/Assume:.+/g, '') .replace(/Q: /, '')|| '';
  };

  // Format response for markdown rendering
  const formatResponseForMarkdown = (text: string) => {
    let formattedText = text;

    // Handle Q: and Assume: patterns for markdown
    formattedText = formattedText.replace(
      /Q:\s*([^\n]*)/gi,
      '**Q:** $1'
    );

    formattedText = formattedText.replace(
      /Assume:\s*([^\n]*)/gi,
      '• $1'
    );

    return formattedText;
  };

  // Focus textarea when popup opens, but ensure scroll stays at top
  useEffect(() => {
    if (popupState.isOpen && followUpTextareaRef.current) {
      // Small delay to ensure popup animation completes
      setTimeout(() => {
        followUpTextareaRef.current?.focus({ preventScroll: true });
        // Ensure we scroll back to top after focus
        setTimeout(() => {
          scrollToTop();
        }, 50);
      }, 300);
    }
  }, [popupState.isOpen, scrollToTop]);

  if (!popupState.isOpen || !popupState.response) {
    return null;
  }

  const isError = popupState.response.includes('❌ Error:');

  return (
    <div 
      ref={overlayRef}
      className={popupStyles.overlay} 
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="popup-title"
    >
      <div className={`${popupStyles.modal} ${isLoadingFollowUp ? popupStyles.asking : ''}`}>
        <div className={popupStyles.header}>
          <h2 id="popup-title" className={popupStyles.title}>
            {/* <Bot size={20} className={responsePopupStyles.iconMargin} /> */}
            Q: {originalQuestion || MESSAGES.POPUP_TITLE}
          </h2>
          <button
            className={popupStyles.closeButton}
            onClick={onClose}
            aria-label={LABELS.ARIA_CLOSE_POPUP}
            type="button"
          >
            {LABELS.CLOSE}
          </button>
        </div>

        <div ref={contentRef} className={popupStyles.content}>
          {/* Hide chat sections when quote generator should be shown */}
          <>
            {popupState.imageAnalyzed && (
              <div className={popupStyles.imageAnalyzedBadge}>
                <ImageIcon size={16} className={responsePopupStyles.smallIconMargin} />
                {MESSAGES.IMAGE_ANALYZED_BADGE}
              </div>
            )}

            <div
              className={`${popupStyles.responseContent} ${isError ? popupStyles.errorMessage : ''}`}
            >
              <ReactMarkdown>{formatResponseForMarkdown(getLatestResponseText())}</ReactMarkdown>
              {isStreaming && <span className="typing-cursor">|</span>}
            </div>

            {/* Quote Generator disabled - AI provides estimates in text response */}
            {false ? (
              <div>Quote Generator Disabled</div>
            ) : (
              /* Follow-up Input Section - Only show when no quote is displayed */
              !popupState.shouldShowEndPrompt && (
                <div className={popupStyles.followUpSection}>
                  <div className={popupStyles.followUpInput}>
                    <textarea
                      ref={followUpTextareaRef}
                      value={followUpMessage}
                      onChange={(e) => setFollowUpMessage(e.target.value)}
                      onKeyDown={handleKeyDown}
                      className={popupStyles.followUpTextarea}
                      placeholder={PLACEHOLDERS.FOLLOWUP_QUESTION}
                      disabled={isLoadingFollowUp}
                      rows={1}
                      maxLength={750}
                    />

                    <button
                      onClick={handleFollowUpSubmit}
                      disabled={!followUpMessage.trim() || isLoadingFollowUp}
                      className={popupStyles.sendButton}
                      type="button"
                    >
                      {isLoadingFollowUp && (
                        <span className={popupStyles.loadingSpinner} />
                      )}
                      {buttonState === 'sending' ? LABELS.SENDING :
                       buttonState === 'receiving' ? LABELS.RECEIVING :
                       LABELS.SEND}
                    </button>
                  </div>

                  {/* Assumption Options - Show when response contains Assume: statements */}
                  {!isError && (
                    <AssumptionOptions
                      response={popupState.response}
                      followUpHistory={followUpHistory}
                      onAssumptionSelect={handleAssumptionSelect}
                      disabled={isLoadingFollowUp}
                    />
                  )}
                </div>
              )
            )}

            {/* Rotating CTA - Show after each AI response, but hide when quote is displayed */}
            {!isError && popupState.shouldShowEndPrompt && (
              <RotatingCTA
                userResponseCount={popupState.userResponseCount || 0}
              />
            )}
          </>
        </div>
      </div>
    </div>
  );
};

export { ResponsePopup };
export default ResponsePopup;
