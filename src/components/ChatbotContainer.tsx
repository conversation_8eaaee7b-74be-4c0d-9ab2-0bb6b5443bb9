import React, { useState, useCallback } from 'react';
import { ClipboardList } from 'lucide-react';
import { FormSubmission } from './FormSubmission';
import { ResponsePopup } from './ResponsePopup';
import { useStateManager } from '@/hooks/useStateManager';
import { useChatHistory } from '@/hooks/useChatHistory';
import { usePopupManager } from '@/hooks/usePopupManager';
import { useErrorHandler, ERROR_CONTEXTS } from '@/hooks/useErrorHandler';
import { apiService } from '@/services/api';
import { ValidationUtils } from '@/utils/validation';
import { MESSAGES, LABELS, PLACEHOLDERS, VALIDATION_MESSAGES } from '@/constants';
import type { FormData as ChatFormData, StreamingCallbacks } from '@/types';
import formStyles from '@/styles/forms.module.scss';
import layoutStyles from '@/styles/layout.module.scss';
import buttonStyles from '@/styles/buttons.module.scss';
import chatbotStyles from '@/styles/chatbot.module.scss';

interface ChatbotContainerProps {
  onNotification: (message: string, type: 'info' | 'success' | 'warning' | 'error') => void;
  className?: string;
}

const ChatbotContainer: React.FC<ChatbotContainerProps> = ({
  onNotification,
  className
}) => {
  const { state, updateState } = useStateManager();
  const { lastConversation, addConversation, addFollowUp, updateQuoteData, clearHistory } = useChatHistory();
  const { handleError } = useErrorHandler({ onNotification });
  const {
    popupState,
    openPopup,
    closePopup,
    openWithApiResponse,
    openWithConversation,
    updatePopupState
  } = usePopupManager();
  const [message, setMessage] = useState('');
  const [streamingResponse, setStreamingResponse] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingMetadata, setStreamingMetadata] = useState<{
    conversationId?: string;
    shouldShowEndPrompt?: boolean;
    userResponseCount?: number;
  }>({});
  const [lastApiResponse, setLastApiResponse] = useState<any>(null);
  const [showNewQuoteButton, setShowNewQuoteButton] = useState(false);

  const handleSubmit = useCallback(async () => {
    // Validate form data - only message is required
    if (!message.trim()) {
      onNotification('Please enter a message', 'error');
      return;
    }

    // For new submissions from home page, always start fresh conversation
    // This ensures each new message from home page starts a new conversation
    let conversationHistory: any[] = [];
    let conversationId: string | undefined;

    const formData: ChatFormData = {
      message: message.trim(),
      selectedImage: null, // Image upload is hidden
      selectedFormat: state.selectedFormat,
      conversationId,
      assumptionMode: false, // Default to false since we removed the toggle
    };

    updateState({ isLoading: true });

    setIsStreaming(true);
    setStreamingResponse('');

    // Create streaming callbacks
    const callbacks: StreamingCallbacks = {
      onMetadata: (metadata) => {
        console.log('Streaming metadata received:', metadata);
        setStreamingMetadata(metadata);

        // Open popup immediately with streaming response
        openPopup({
          response: '',
          imageAnalyzed: false,
          conversationId: metadata.conversationId,
          followUpHistory: [],
          shouldShowEndPrompt: metadata.shouldShowEndPrompt,
          userResponseCount: metadata.userResponseCount,
        });
      },
      onContent: (content, tokenCount) => {
        console.log(`Streaming content: ${content} (token ${tokenCount})`);
        setStreamingResponse(prev => {
          const newResponse = prev + content;
          // Update popup with streaming response
          updatePopupState({
            response: newResponse,
          });
          return newResponse;
        });
      },
      onComplete: (data) => {
        console.log('🎯 Streaming complete - onComplete callback called:', data);
        setIsStreaming(false);
        updateState({ isLoading: false });

        // Add to chat history with final response - use current state value
        setStreamingResponse(currentResponse => {
          console.log('Saving conversation with response:', currentResponse);
          addConversation(
            formData.message,
            currentResponse,
            false,
            streamingMetadata.conversationId
          );
          return currentResponse; // Keep the response for now
        });

        // Update popup with final data
        updatePopupState({
          shouldShowEndPrompt: data.shouldAutoGenerateQuote || streamingMetadata.shouldShowEndPrompt || false,
        });

        // Clear the message input after successful submission
        setMessage('');

        // Clear streaming response after a short delay to ensure conversation is saved
        setTimeout(() => {
          setStreamingResponse('');
        }, 100);
      },
      onError: (error, details) => {
        console.error('Streaming error:', error, details);
        setIsStreaming(false);
        updateState({ isLoading: false });

        handleError(new Error(error), ERROR_CONTEXTS.CHAT_SUBMISSION);

        const errorResponse = `❌ Error: ${error}\n\n${VALIDATION_MESSAGES.BACKEND_SERVER_ERROR}`;

        // Add error to chat history
        const conversation = addConversation(
          formData.message,
          errorResponse,
          false
        );

        // Show error in popup
        openPopup({
          response: errorResponse,
          imageAnalyzed: false,
          conversationId: conversation.id,
          followUpHistory: [],
          shouldShowEndPrompt: false,
          userResponseCount: 0,
        });

        setStreamingResponse('');
      }
    };

    try {
      // Use streaming API
      await apiService.sendMessageStream(formData, conversationHistory, callbacks);
    } catch (error) {
      // Fallback to regular API if streaming fails
      console.warn('Streaming failed, falling back to regular API:', error);

      try {
        const apiResponse = await apiService.sendMessage(formData, conversationHistory);

        // Store the API response for use in ResponsePopup
        setLastApiResponse(apiResponse);

        // Add to chat history
        const conversation = addConversation(
          formData.message,
          apiResponse.response,
          false,
          apiResponse.conversationId
        );

        // Open popup with response
        openWithApiResponse(apiResponse, conversation);

        // Clear the message input after successful submission
        setMessage('');
      } catch (fallbackError) {
        handleError(fallbackError, ERROR_CONTEXTS.CHAT_SUBMISSION);

        const errorMessage = fallbackError instanceof Error ? fallbackError.message : VALIDATION_MESSAGES.UNEXPECTED_ERROR;
        const errorResponse = `❌ Error: ${errorMessage}\n\n${VALIDATION_MESSAGES.BACKEND_SERVER_ERROR}`;

        // Add error to chat history
        const conversation = addConversation(
          formData.message,
          errorResponse,
          false
        );

        // Show error in popup
        openPopup({
          response: errorResponse,
          imageAnalyzed: false,
          conversationId: conversation.id,
          followUpHistory: [],
          shouldShowEndPrompt: false,
          userResponseCount: 0,
        });
      } finally {
        updateState({ isLoading: false });
        setIsStreaming(false);
        setStreamingResponse('');
      }
    }
  }, [message, state.selectedFormat, updateState, handleError, addConversation, lastConversation, openWithApiResponse, openPopup, updatePopupState, streamingResponse, streamingMetadata, setLastApiResponse]);

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleSubmit();
    }
  }, [handleSubmit]);

  const handleClosePopup = useCallback(() => {
    closePopup();
    setShowNewQuoteButton(false);
  }, [closePopup]);

  const handleRequestNewQuote = useCallback(() => {
    closePopup();
    setShowNewQuoteButton(false);
    setMessage('');
    setLastApiResponse(null);
  }, [closePopup]);

  const handleAskAgain = useCallback(() => {
    // Close the popup
    closePopup();
    // Clear the last API response and quote data
    setLastApiResponse(null);
    // Clear chat history to remove the last conversation
    clearHistory();
    // Clear the message input
    setMessage('');
    // Reset any other state
    setShowNewQuoteButton(false);
  }, [closePopup, clearHistory]);

  const handleReopenLastResponse = useCallback(() => {
    if (lastConversation) {
      openWithConversation(lastConversation);
    } else {
      console.log('No last conversation available');
    }
  }, [lastConversation, openWithConversation]);

  const handleFollowUpAdded = useCallback(async (conversationId: string, question: string, response: string, shouldShowEndPrompt?: boolean, userResponseCount?: number) => {
    addFollowUp(conversationId, question, response);

    // Update popup state with the conversation state from the actual API response
    // (passed from ResponsePopup after the real API call)
    if (shouldShowEndPrompt !== undefined || userResponseCount !== undefined)
      updatePopupState({
        shouldShowEndPrompt: shouldShowEndPrompt || false,
        userResponseCount: userResponseCount || 0,
      });
  }, [addFollowUp, updatePopupState]);

  return (
    <div className={`${layoutStyles.chatbotContainer} ${className || ''}`}>
      {/* Hide main card when quote is being shown, unless new quote button is active */}
      {(!(popupState.isOpen && popupState.shouldShowEndPrompt) || showNewQuoteButton) && (
        <div className={`${layoutStyles.mainCard} ${state.isLoading ? layoutStyles.loading : ''}`}>
          <h1 className={layoutStyles.title}>{MESSAGES.MAIN_TITLE}</h1>

          <p className={layoutStyles.description}>
            {MESSAGES.MAIN_DESCRIPTION}
          </p>

          <div className={layoutStyles.inputSection}>
            <div className={chatbotStyles.relativeContainer}>
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                className={`${formStyles.projectInput} ${
                  message.length > 675 ? 'error' : message.length > 600 ? 'warning' : ''
                }`}
                placeholder={PLACEHOLDERS.PROJECT_DESCRIPTION}
                rows={2}
                maxLength={750}
                disabled={state.isLoading}
                aria-label={LABELS.ARIA_PROJECT_DESCRIPTION}
              />
              <div className={`${layoutStyles.characterCounter} ${
                message.length > 675 ? 'error' : message.length > 600 ? 'warning' : ''
              }`}>
                {message.length}{LABELS.CHARACTER_COUNTER_SEPARATOR}750{LABELS.CHARACTERS_SUFFIX}
              </div>
            </div>

            <div className={layoutStyles.actionButtons}>
              {/* <ImageUpload
                selectedImage={state.selectedImage}
                onImageChange={handleImageChange}
                disabled={state.isLoading}
              /> */}


              <FormSubmission
                onSubmit={handleSubmit}
                isLoading={state.isLoading}
                disabled={!message.trim()}
              />

              {lastConversation && (
                <button
                  type="button"
                  className={`${buttonStyles.btnOutline} ${chatbotStyles.reopenButton}`}
                  onClick={handleReopenLastResponse}
                  disabled={state.isLoading}
                  title={`Reopen: ${lastConversation.originalMessage?.substring(0, 50)}...`}
                >
                  <ClipboardList size={16} className={chatbotStyles.iconMargin} />
                  {LABELS.REOPEN_LAST_RESPONSE}
                </button>
              )}

              {/* Debug info */}
              {/* {process.env.NODE_ENV === 'development' && (
                <div style={{ fontSize: '10px', color: '#666', marginTop: '5px' }}>
                  Last conversation: {lastConversation ? 'Available' : 'None'}
                  {lastConversation && ` (ID: ${lastConversation.id?.substring(0, 8)}...)`}
                </div>
              )} */}
            </div>

          </div>
        </div>
      )}

      <ResponsePopup
        popupState={popupState}
        onClose={handleClosePopup}
        onNotification={onNotification}
        onFollowUpAdded={handleFollowUpAdded}
        projectDescription={lastConversation?.originalMessage || message}
        projectFormat={state.selectedFormat}
        assumptionMode={false}
        autoQuoteData={lastApiResponse?.quoteData || lastConversation?.quoteData}
        onRequestNewQuote={handleRequestNewQuote}
        onQuoteGenerated={(quote: any) => {
          if (popupState.conversationId) {
            updateQuoteData(popupState.conversationId, quote);
          }
        }}
        onAskAgain={handleAskAgain}
        isStreaming={isStreaming}
        streamingResponse={streamingResponse}
        originalQuestion={lastConversation?.originalMessage || message}
      />
    </div>

  );
};

export { ChatbotContainer };
export default ChatbotContainer;
