import type { ValidationResult } from '@/types';
import { ALLOWED_IMAGE_TYPES, MAX_FILE_SIZE, MAX_MESSAGE_LENGTH, MIN_MESSAGE_LENGTH, VALIDATION_PATTERNS } from '@/types';
import { VALIDATION_MESSAGES, formatValidationMessage } from '@/constants';

export class ValidationUtils {
  /**
   * Validate project message
   */
  static validateMessage(message: string): ValidationResult {
    if (!message || message.trim().length === 0) {
      return {
        isValid: false,
        error: VALIDATION_MESSAGES.DESCRIBE_PROJECT,
      };
    }

    if (message.trim().length < MIN_MESSAGE_LENGTH) {
      return {
        isValid: false,
        error: formatValidationMessage(VALIDATION_MESSAGES.MESSAGE_TOO_SHORT, { min: MIN_MESSAGE_LENGTH }),
      };
    }

    if (message.length > MAX_MESSAGE_LENGTH) {
      return {
        isValid: false,
        error: formatValidationMessage(VALIDATION_MESSAGES.MESSAGE_TOO_LONG, { max: MAX_MESSAGE_LENGTH }),
      };
    }

    return { isValid: true };
  }

  /**
   * Validate uploaded image file
   */
  static validateImageFile(file: File): ValidationResult {
    if (!file) {
      return {
        isValid: false,
        error: VALIDATION_MESSAGES.NO_FILE_SELECTED,
      };
    }

    // Check file type
    if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
      return {
        isValid: false,
        error: VALIDATION_MESSAGES.INVALID_IMAGE_FILE,
      };
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      const maxSizeMB = MAX_FILE_SIZE / (1024 * 1024);
      return {
        isValid: false,
        error: formatValidationMessage(VALIDATION_MESSAGES.FILE_TOO_LARGE, { maxSize: maxSizeMB }),
      };
    }

    return { isValid: true };
  }

  /**
   * Validate form data
   */
  static validateFormData(message: string, image: File | null): ValidationResult {
    // Only require message - image is completely optional
    if (!message || message.trim().length === 0) {
      return {
        isValid: false,
        error: VALIDATION_MESSAGES.DESCRIBE_PROJECT,
      };
    }

    // Validate message
    const messageValidation = this.validateMessage(message);
    if (!messageValidation.isValid) {
      return messageValidation;
    }

    // Validate image if provided (but it's optional)
    if (image) {
      const imageValidation = this.validateImageFile(image);
      if (!imageValidation.isValid) {
        return imageValidation;
      }
    }

    return { isValid: true };
  }

  /**
   * Validate project format
   */
  static validateFormat(format: string): ValidationResult {
    if (!format || format.trim().length === 0) {
      return {
        isValid: false,
        error: VALIDATION_MESSAGES.SELECT_PROJECT_FORMAT,
      };
    }

    return { isValid: true };
  }

  /**
   * Sanitize HTML content
   */
  static sanitizeHtml(html: string): string {
    const div = document.createElement('div');
    div.textContent = html;
    return div.innerHTML;
  }

  /**
   * Validate email format
   */
  static validateEmail(email: string): ValidationResult {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!email || email.trim().length === 0) {
      return {
        isValid: false,
        error: VALIDATION_MESSAGES.EMAIL_REQUIRED,
      };
    }

    if (!emailRegex.test(email)) {
      return {
        isValid: false,
        error: VALIDATION_MESSAGES.INVALID_EMAIL,
      };
    }

    return { isValid: true };
  }

  /**
   * Validate URL format
   */
  static validateUrl(url: string): ValidationResult {
    if (!url || url.trim().length === 0) {
      return {
        isValid: false,
        error: VALIDATION_MESSAGES.URL_REQUIRED,
      };
    }

    try {
      new URL(url);
      return { isValid: true };
    } catch {
      return {
        isValid: false,
        error: VALIDATION_MESSAGES.INVALID_URL,
      };
    }
  }
}

export default ValidationUtils;
