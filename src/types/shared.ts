// Shared types between frontend and backend
// This file should eventually be moved to a shared package or directory

// Conversation state types (used by both frontend and backend)
export interface ConversationState {
  isVague: boolean;
  hasEnoughInfo: boolean;
  questionCount: number;
  hasComprehensiveResponse: boolean;
  userFrustration: boolean;
  shouldShowEndPrompt: boolean;
  shouldAutoGenerateQuote: boolean;
  userResponseCount: number;
  assumptionMode: boolean;
}

// API request/response types
export interface ApiRequestBase {
  conversationId?: string;
  assumptionMode?: boolean;
}

export interface ChatRequest extends ApiRequestBase {
  message: string;
  selectedImage?: File;
  selectedFormat: string;
}

export interface FollowUpRequest extends ApiRequestBase {
  message: string;
}

export interface FigmaRequest extends ApiRequestBase {
  figmaUrl: string;
  format: string;
}

// Common response structure
export interface ApiResponseBase {
  response: string;
  conversationId: string;
  shouldShowEndPrompt?: boolean;
  userResponseCount?: number;
  conversationState?: ConversationState;
}

export interface ChatApiResponse extends ApiResponseBase {
  imageAnalyzed?: boolean;
  quoteData?: any; // TODO: Replace with proper QuoteData type
}

export interface FollowUpApiResponse extends ApiResponseBase {
  // Follow-up specific fields can be added here
}

export interface FigmaApiResponse extends ApiResponseBase {
  figmaData: {
    fileName: string;
    // Add other Figma-specific fields as needed
  };
}

// Error response structure
export interface ApiErrorResponse {
  error: string;
  details?: string;
  timestamp?: string;
}

// HTTP status codes commonly used
export enum HttpStatusCode {
  OK = 200,
  CREATED = 201,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503,
}

// Common validation patterns
export const VALIDATION_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  FIGMA_URL: /^https:\/\/www\.figma\.com\/(file|proto)\/[a-zA-Z0-9]+/,
  MESSAGE_MAX_LENGTH: 750,
  MESSAGE_MIN_LENGTH: 1,
  IMAGE_MAX_SIZE: 10 * 1024 * 1024, // 10MB
  IMAGE_ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
} as const;