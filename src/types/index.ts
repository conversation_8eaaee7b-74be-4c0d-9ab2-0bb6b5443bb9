// Main type definitions for the AI Chatbot application
export * from './shared';

export interface ProjectFormat {
  id: string;
  label: string;
  value: string;
}

export interface ChatbotState {
  selectedFormat: string;
  selectedImage: File | null;
  isLoading: boolean;
  dropdownOpen: boolean;
  assumptionMode: boolean;
}

export interface ApiResponse {
  response: string;
  imageAnalyzed?: boolean;
  error?: string;
  conversationId?: string;
  shouldShowEndPrompt?: boolean;
  shouldAutoGenerateQuote?: boolean;
  quoteData?: QuoteData;
  userResponseCount?: number;
}

export interface StreamingCallbacks {
  onContent: (content: string, tokenCount: number) => void;
  onMetadata: (metadata: { conversationId: string; shouldShowEndPrompt: boolean; userResponseCount: number }) => void;
  onComplete: (data: { shouldAutoGenerateQuote: boolean; quoteData: any; totalTokens: number }) => void;
  onError: (error: string, details?: string) => void;
}

export interface PopupState {
  isOpen: boolean;
  response: string | null;
  imageAnalyzed: boolean;
  conversationId?: string;
  followUpHistory: FollowUpMessage[];
  shouldShowEndPrompt?: boolean;
  userResponseCount?: number;
}

export interface FollowUpMessage {
  id: string;
  question: string;
  response: string;
  timestamp: Date;
}

export interface FollowUpData {
  message: string;
  conversationId?: string;
  assumptionMode?: boolean;
}

export interface ChatHistory {
  id: string;
  timestamp: Date;
  originalMessage: string;
  originalResponse: string;
  imageAnalyzed: boolean;
  followUpHistory: FollowUpMessage[];
  conversationId?: string;
  quoteData?: QuoteData;
  shouldShowEndPrompt?: boolean;
}

export interface ChatHistoryState {
  conversations: ChatHistory[];
  lastConversation: ChatHistory | null;
}

export interface UnsplashImage {
  id: string;
  urls: {
    raw: string;
    full: string;
    regular: string;
    small: string;
    thumb: string;
  };
  user: {
    name: string;
    username: string;
    links: {
      html: string;
    };
  };
  links: {
    html: string;
  };
}

export interface FormData {
  message: string;
  selectedImage: File | null;
  selectedFormat: string;
  conversationId?: string;
  assumptionMode?: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export interface NotificationProps extends BaseComponentProps {
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  duration?: number;
  onClose?: () => void;
  visible: boolean;
}

export interface DropdownItem {
  value: string;
  label: string;
}

// Component Props Types
export interface ChatbotContainerProps {
  className?: string;
}

export interface FormatSelectorProps {
  selectedFormat: string;
  onFormatChange: (format: string) => void;
  disabled?: boolean;
  className?: string;
}

export interface ImageUploadProps {
  selectedImage: File | null;
  onImageChange: (file: File | null) => void;
  disabled?: boolean;
  className?: string;
}

export interface FormSubmissionProps {
  formData: FormData;
  isLoading: boolean;
  onSubmit: (data: FormData) => Promise<void>;
  onLoadingChange: (loading: boolean) => void;
  className?: string;
}

// Event Types
export interface FormatChangeEvent extends CustomEvent {
  detail: {
    format: string;
  };
}

export interface ImageChangeEvent extends CustomEvent {
  detail: {
    file: File | null;
  };
}

export interface ResponseReceivedEvent extends CustomEvent {
  detail: {
    response: string;
    imageAnalyzed: boolean;
  };
}

// Quote Generator Types
export interface ProjectEstimate {
  developmentHours: number;
  projectManagementHours: number;
  designHours: number;
  qaHours: number;
  infrastructureHours: number;
  maintenanceHours: number;
  hourlyRate: number;
  complexity: 'small' | 'medium' | 'large';
  projectType: string;
  riskFactor: number;
}

export interface QuoteData {
  traditional: ProjectEstimate & { total: number; breakdown: CostBreakdown };
  aiDriven: ProjectEstimate & { total: number; breakdown: CostBreakdown };
  savings: {
    amount: number;
    percentage: number;
  };
  timeline: {
    traditional: number;
    aiDriven: number;
  };
  confidence: number;
  bestOption?: 'traditional' | 'aiDriven';
  bestOptionReason?: string;
}

export interface CostBreakdown {
  development: number;
  projectManagement: number;
  design: number;
  qa: number;
  infrastructure: number;
  maintenance: number;
}

// Base Component Props
export interface BaseComponentProps {
  className?: string;
  disabled?: boolean;
}

export interface LoadableComponentProps extends BaseComponentProps {
  isLoading?: boolean;
}

export interface InteractiveComponentProps extends BaseComponentProps {
  onClick?: () => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
}

// Hook Types
export interface UseStateManager {
  state: ChatbotState;
  updateState: (updates: Partial<ChatbotState>) => void;
  resetState: () => void;
}

export interface UseUnsplashBackground {
  refreshBackground: () => void;
  isLoading: boolean;
  error: string | null;
  attributionElement: React.ReactElement | null;
}

// Constants
export const PROJECT_FORMATS: ProjectFormat[] = [
  { id: 'web', label: 'Web', value: 'web' },
  { id: 'webapp', label: 'Webapp', value: 'webapp' },
  { id: 'backend-api', label: 'Backend API', value: 'backend-api' },
  { id: 'database-integration', label: 'Database Integration', value: 'database-integration' },
  { id: 'model-training', label: 'Model Training', value: 'model-training' },
  { id: 'hybrid-webapp', label: 'Hybrid Webapp', value: 'hybrid-webapp' },
  { id: 'hybrid-platform', label: 'Hybrid Platform', value: 'hybrid-platform' },
  { id: 'microservices', label: 'Microservices', value: 'microservices' },
  { id: 'cloud-native', label: 'Cloud Native', value: 'cloud-native' },
  { id: 'real-time-app', label: 'Real-time Application', value: 'real-time-app' },
  { id: 'blockchain-app', label: 'Blockchain Application', value: 'blockchain-app' },
  { id: 'iot-integration', label: 'IoT Integration', value: 'iot-integration' },
  { id: 'ai-ml-integration', label: 'AI/ML Integration', value: 'ai-ml-integration' },
  { id: 'enterprise-system', label: 'Enterprise System', value: 'enterprise-system' },
];

export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp'
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const MAX_MESSAGE_LENGTH = 750;
export const MIN_MESSAGE_LENGTH = 10;
