import React, { useEffect } from 'react';
import ChatbotContainer from '@/components/ChatbotContainer';
import NotificationContainer from '@/components/NotificationContainer';
import { useUnsplashBackground } from '@/hooks/useUnsplashBackground.tsx';
import { useNotification } from '@/hooks/useNotification';
import layoutStyles from '@/styles/layout.module.scss';

const App: React.FC = () => {
  const { attributionElement, setBackgroundImage } = useUnsplashBackground();
  const { notifications, showNotification, hideNotification } = useNotification();

  useEffect(() => {
    setBackgroundImage();
  }, [setBackgroundImage]);

  return (
    <div className={layoutStyles.app}>
      
      <ChatbotContainer 
        onNotification={showNotification}
      />
      
      <NotificationContainer
        notifications={notifications}
        onClose={hideNotification}
      />
      
      {attributionElement}
    </div>
  );
};

export default App;
