# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an AI-powered chatbot application for estimating custom software projects with intelligent conversation management. The application uses React TypeScript frontend with a Node.js Express backend that integrates with OpenAI's API.

**Key Innovation**: The AI includes sophisticated conversation analysis that detects comprehensive responses (like "all of those things") and automatically switches from questioning mode to solution mode, preventing endless question loops.

## Architecture

### Monorepo Structure
- **Frontend**: React 18 + TypeScript 5 + Vite (root directory)
- **Backend**: Node.js + Express + TypeScript (backend/ directory)  
- **Shared**: Both use ES modules (`"type": "module"`)

### Frontend State Management Pattern
The application uses a dual-hook pattern for state management:
- `useStateManager`: Manages UI state (loading, dropdowns, format selection)
- `useChatHistory`: Manages conversation persistence with localStorage, handles conversation threading and follow-up messages
- `usePopupManager`: Manages popup states and transitions
- `useErrorHandler`: Centralized error handling with user notifications

### Conversation Intelligence Architecture
The backend includes a sophisticated conversation analysis system in `backend/prompts/generators.ts`:
- **Conversation State Analysis**: Tracks question count (now 2 questions max), response patterns, and user frustration indicators
- **Dynamic Prompt Generation**: Creates context-aware system prompts based on conversation state
- **Response Detection**: Identifies comprehensive responses using pattern matching (e.g., "all of those things", "everything you mentioned")
- **Mode Switching**: Automatically transitions between questioning mode and solution mode after 2 Q&A cycles

### Streaming Architecture
The application supports real-time streaming responses:
- **Frontend**: `ChatbotContainer` handles streaming callbacks (`onMetadata`, `onContent`, `onComplete`, `onError`)
- **Backend**: Streaming endpoints at `/api/chat/stream` and `/api/followup/stream`
- **Fallback**: Automatically falls back to regular API if streaming fails
- **State Management**: Streaming responses are managed separately from final conversation storage

### API Communication Pattern
The frontend `ApiService` (singleton pattern) handles:
- **Streaming Support**: Primary mode with automatic fallback to regular API
- **Conversation History**: Preservation across requests with full message context
- **Error Handling**: Comprehensive error handling with user feedback
- **Endpoint Management**: Separate endpoints for initial chat, follow-up, image uploads, and streaming

## Development Commands

### Root Level Commands
- `npm run dev` - Start both frontend (port 5173) and backend (port 3001) concurrently
- `npm run dev:frontend` - Start only Vite dev server
- `npm run dev:backend` - Start only backend with tsx watch
- `npm run build` - Build frontend for production
- `npm run type-check` - TypeScript type checking without emit
- `npm install:backend` - Install backend dependencies

### API Configuration Commands
- `npm run api:local` - Configure frontend to use localhost API (http://localhost:3001)
- `npm run api:deployed` - Configure frontend to use deployed AWS API
- `npm run api:status` - Check current API configuration

### Backend Commands (from backend/ directory)
- `npm run dev` - Development server with tsx watch
- `npm run build` - Compile TypeScript to JavaScript
- `npm start` - Production server (build + run)

### AWS Deployment Commands
**Important**: Always run `npm run set:credentials` first to ensure correct AWS account
- `npm run deploy:frontend` - Deploy frontend to S3/CloudFront
- `npm run deploy:frontend:update` - Update existing frontend deployment
- `npm run deploy:backend` - Full backend deployment to Lambda/API Gateway
- `npm run deploy:backend:update` - Update existing Lambda function code only
- `npm run deploy:full` - Deploy both frontend and backend
- `npm run status:all` - Check status of all deployed resources

### Monitoring & Debugging Commands
- `npm run logs` - Tail AWS Lambda logs in real-time
- `npm run logs:recent` - Show recent Lambda logs
- `npm run logs:errors` - Filter Lambda logs for errors only
- `npm run connect:backend` - Connect to backend for debugging
- `npm run test:backend` - Test backend connection

## Key Technologies & Patterns

### Frontend Stack
- **React 18** with functional components and hooks
- **TypeScript** with strict mode and path aliases (`@/` prefix)
- **Vite** for build tooling with React plugin
- **SCSS Modules** with CSS variables for theming
- **Lucide React** for icons

### Backend Stack  
- **Express** server with TypeScript compilation
- **OpenAI API** (GPT-4o) with streaming support
- **CORS** middleware for cross-origin requests
- **AWS Lambda** deployment with serverless-express

### Backend Services Architecture
- **ConversationService**: In-memory conversation storage using Map data structure
- **ChatService**: Main business logic for processing chat requests and conversation state analysis
- **StorageService**: Manages conversation persistence to JSON files in `backend/chats/`
- **FigmaService**: Handles Figma URL analysis and design extraction

### Frontend Hook Architecture
- **useStateManager**: Manages UI state with callback-optimized state updates
- **useChatHistory**: Handles conversation persistence with localStorage, includes automatic cleanup (50 conversation limit)
- **usePopupManager**: Manages popup states, transitions, and API response handling
- **useErrorHandler**: Centralized error handling with context-aware user notifications
- **useNotification**: Toast notification system with auto-cleanup
- **useUnsplashBackground**: Simplified background management (sets black background)

## Environment Setup

### Frontend (.env in root)
```
VITE_API_BASE_URL=http://localhost:3001/api  # For local development
# OR
VITE_API_BASE_URL=https://your-api-gateway-url/prod/api  # For deployed API
```

### Backend (.env in backend/)
```
OPENAI_API_KEY=your_key_here  # Required
PORT=3001                     # Optional, defaults to 3001
```

### AWS Deployment (.env in root)
```
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
```

## Code Conventions

### TypeScript Configuration
- Uses strict mode with `noUnusedLocals` and `noUnusedParameters`
- Path aliases configured for cleaner imports (`@/` prefix)
- React JSX transform enabled

### Component Patterns
- Functional components with TypeScript interfaces
- Props destructuring with explicit typing
- Custom hooks for reusable logic
- CSS Modules for scoped styling
- Memoization with `useCallback` and `useMemo` for performance

### API Communication
- All API calls go through `src/services/api.ts`
- Streaming-first approach with automatic fallback
- Conversation context is maintained across requests
- Comprehensive error handling with user notifications

## Testing & Quality

There are no configured testing frameworks. When adding tests:
- Check for existing test scripts in package.json
- Look for test configuration files
- Ask user for preferred testing approach

## Important Implementation Details

### Conversation Flow Logic
- **2 Questions Maximum**: After exactly 2 questions asked by AI, the system automatically shows end prompt and generates comprehensive quote
- **Streaming Responses**: Primary mode for real-time user feedback with fallback to regular API
- **State Synchronization**: Conversation state is tracked across frontend (localStorage) and backend (in-memory Map + JSON persistence)
- **User Pattern Detection**: System detects user frustration patterns and comprehensive responses to switch modes appropriately

### File Structure Conventions
- All frontend imports use `@/` path aliases (configured in vite.config.ts)
- Backend uses ES modules with `.js` import extensions for TypeScript files
- Conversation history stored in `backend/chats/` as timestamped JSON files
- **Image Upload Disabled**: ImageUpload component is commented out but infrastructure preserved

### API Endpoints
- `/api/chat` - Initial chat requests with format selection
- `/api/chat/stream` - Streaming version of initial chat
- `/api/followup` - Follow-up questions within existing conversations  
- `/api/followup/stream` - Streaming version of follow-up
- `/api/upload` - Image upload with analysis capabilities (disabled in UI)
- `/api/figma/analyze` - Figma URL analysis and design extraction
- `/api/health` - Health check endpoint

### State Management Patterns
- **Singleton ApiService**: Consistent API communication across components
- **Conversation IDs**: Follow pattern `chat_${timestamp}_${randomId}` or `conv_${timestamp}_${randomId}`
- **Optimized Re-renders**: All state updates use useCallback optimization
- **Error Context**: Centralized error handling with component-specific context
- **Streaming State**: Separate state management for streaming vs final responses

### AWS Deployment Architecture
- **Frontend**: S3 bucket with CloudFront distribution
- **Backend**: Lambda function with API Gateway
- **Credentials Management**: Uses `~/.aws/projects/internal` for deployment credentials
- **Environment Switching**: Scripts to switch between local and deployed API endpoints
- **Monitoring**: CloudWatch logs with real-time streaming support

### Recent Architectural Changes
- **Removed Components**: AssumptionToggle, BackgroundRefresh, ChatForm, FigmaIntegration, FormatSelector (cleaned up unused code)
- **Hidden Features**: Image upload functionality is hidden from UI but backend support remains
- **Streaming Integration**: Full streaming support with fallback mechanisms
- **Question Limit**: Reduced from 3 to 2 questions maximum for faster user experience
- **Error Handling**: Enhanced error handling with contextual user feedback