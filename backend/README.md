# AI Chatbot Backend

## Overview
This is the backend server for the AI Chatbot project. It provides intelligent project estimation and consultation services with advanced conversation management, designed to understand user intent and provide comprehensive solutions when users give affirmative responses like "all of those things."

## Key Features

### Intelligent Conversation Management
- **Smart Response Detection**: Automatically detects comprehensive affirmative responses like "all of those things", "everything you mentioned", "yes to all"
- **User Frustration Detection**: Identifies when users are frustrated with too many questions and switches to solution mode
- **Context Preservation**: Maintains full conversation history across multiple interactions for better understanding
- **Adaptive Prompting**: Dynamically adjusts system prompts based on conversation state and user behavior

### Core Functionality
- **Multi-turn Conversations**: Supports complex project estimation through intelligent dialogue
- **Image Analysis**: Upload and analyze design mockups using GPT-4 Vision
- **Conversation Memory**: Tracks conversation state for contextual responses
- **Modular Architecture**: Organized prompt system with dedicated modules for maintainability

## Architecture Improvements

### Conversation Intelligence
The system now includes sophisticated conversation analysis that:
- Detects when users provide comprehensive responses instead of asking more questions
- Recognizes user frustration patterns and adapts accordingly
- Maintains conversation context across multiple API calls
- Provides detailed solutions when users indicate they want comprehensive coverage

### Modular Prompt System
```
backend/
├── prompts/
│   ├── base.ts          # Base prompt templates and rules
│   ├── generators.ts    # Dynamic prompt generation logic
│   └── index.ts         # Main conversation analysis exports
├── server.ts            # Main server with conversation tracking
├── chats/              # Saved conversation sessions
└── uploads/            # Temporary image storage
```

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Create a `.env` file** in the `backend/` directory:
   ```env
   OPENAI_API_KEY=your_openai_api_key_here
   PORT=3001
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```

4. **Configure frontend to use local API:**
   ```bash
   # From the root directory
   npm run api:local    # Configure frontend to use localhost:3001
   npm run dev          # Start frontend
   ```

5. **Or build and run in production:**
   ```bash
   npm run build
   npm start
   ```

## Environment Variables
- `OPENAI_API_KEY`: Your OpenAI API key (required)
- `PORT`: Server port (default: 3001)

## Frontend Integration

The frontend automatically connects to this backend when configured for local development:

```bash
# From root directory - configure frontend for local API
npm run api:local

# Check current frontend API configuration
npm run api:status
```

The backend includes CORS configuration to support both local development (`localhost:5173`) and production domains.

## API Endpoints

### Health Check
- `GET /api/health`
  - Returns server status and health information

### Enhanced Chat Endpoints

#### Initial Chat
- `POST /api/chat`
  - **Request Body:**
    ```json
    {
      "messages": [
        { "role": "user", "content": "I want to build a document generator..." }
      ],
      "format": "web",
      "conversationId": "optional-existing-id"
    }
    ```
  - **Response:**
    ```json
    {
      "response": "AI's intelligent response...",
      "conversationId": "conv_123456789_abc123",
      "usage": { "prompt_tokens": 150, "completion_tokens": 200 }
    }
    ```

#### Follow-up Conversations
- `POST /api/followup`
  - **Request Body:**
    ```json
    {
      "messages": [
        { "role": "user", "content": "All of those things yes." }
      ],
      "conversationId": "conv_123456789_abc123"
    }
    ```
  - **Response:** Comprehensive solution when comprehensive response detected

#### Image Analysis
- `POST /api/upload`
  - **Form Data:**
    - `image`: Image file (jpg, png, gif, webp, max 10MB)
    - `message`: Optional description
    - `format`: Target platform (web, mobile, etc.)
  - **Response:**
    ```json
    {
      "response": "Detailed analysis and recommendations...",
      "conversationId": "conv_123456789_abc123",
      "imageAnalyzed": true,
      "usage": { ... }
    }
    ```

## Conversation Flow

1. **Initial Request**: User submits project description
2. **Intelligent Analysis**: System analyzes:
   - Request complexity and specificity
   - User's previous responses in conversation
   - Signs of user frustration or comprehensive intent
3. **Smart Response Strategy**:
   - **Vague requests**: Ask specific, targeted questions with examples
   - **Comprehensive responses detected**: Provide detailed solutions immediately
   - **User frustration detected**: Switch to comprehensive solution mode
4. **Context Maintenance**: Full conversation history preserved across interactions

## Conversation Intelligence Features

### Comprehensive Response Detection
The system recognizes patterns like:
- "All of those things"
- "Everything you mentioned"
- "Yes to all"
- "Include everything"
- "All the features"

### User Frustration Detection
Identifies phrases indicating user frustration:
- "Stop asking questions"
- "Just tell me"
- "I already said yes"
- "You're asking too many questions"

### Adaptive Behavior
- **Question Mode**: For initial vague requests without design context
- **Analysis Mode**: When Figma URLs or images are provided for design analysis
- **Solution Mode**: When comprehensive responses, frustration detected, or sufficient design data available
- **Context Mode**: Maintains conversation memory and design analysis for better understanding
- **Multi-Modal Mode**: Combines text, design files, and images for comprehensive analysis

## Technologies Used

- **Node.js & Express.js**: Server framework
- **OpenAI API**: GPT-4 and GPT-4 Vision for intelligent responses
- **TypeScript**: Type-safe development
- **Multer**: Secure file upload handling
- **CORS**: Cross-origin resource sharing
- **Advanced Conversation State Management**: Custom conversation tracking

## Development Notes

This backend solves the common AI chatbot problem where users get stuck in endless question loops. When a user says "all of those things," the system now understands this as a comprehensive affirmative response and provides detailed solutions instead of asking more questions.

The conversation analysis system tracks user patterns and adapts the AI's behavior to provide a more natural, helpful experience that respects user intent and reduces frustration.

## License
MIT
