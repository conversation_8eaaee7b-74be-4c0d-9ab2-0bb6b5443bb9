import { Router } from 'express';
import healthRoutes from './healthRoutes.js';
import chatRoutes from './chatRoutes.js';
import uploadRoutes from './uploadRoutes.js';
import figmaRoutes from './figmaRoutes.js';

const router = Router();

// Mount all routes with /api prefix
router.use('/api', healthRoutes);
router.use('/api', chatRoutes);
router.use('/api', uploadRoutes);
router.use('/api/figma', figmaRoutes);

export default router;
