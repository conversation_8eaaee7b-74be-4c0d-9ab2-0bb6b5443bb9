import { Router, Request, Response } from 'express';
import { FigmaService } from '../services/figmaService.js';
import { handleAsync } from '../utils/asyncHandler.js';

const router = Router();
let figmaService: FigmaService | null = null;

const getFigmaService = () => {
  if (!figmaService) {
    figmaService = new FigmaService();
  }
  return figmaService;
};

interface FigmaAnalyzeRequest extends Request {
  body: {
    figmaUrl: string;
    conversationId?: string;
    format?: string;
  };
}

/**
 * POST /api/figma/analyze
 * Analyze a Figma design from URL
 */
router.post('/analyze', handleAsync(async (req: FigmaAnalyzeRequest, res: Response) => {
  const { figmaUrl, conversationId, format } = req.body;

  // Validate required fields
  if (!figmaUrl) {
    return res.status(400).json({
      error: 'Missing required field: figmaUrl'
    });
  }

  // Validate URL format
  if (!getFigmaService().isValidFigmaUrl(figmaUrl)) {
    return res.status(400).json({
      error: 'Invalid Figma URL format. Please provide a valid Figma file, design, or prototype URL.'
    });
  }

  try {
    console.log('Analyzing Figma URL:', figmaUrl);

    // Process Figma URL
    const result = await getFigmaService().processFigmaUrl(figmaUrl);

    // Generate AI response for the chat
    const aiResponse = generateFigmaAnalysisResponse(result.analysis, result.fileData.name);

    // Return structured response
    res.json({
      success: true,
      response: aiResponse,
      figmaData: {
        fileName: result.fileData.name,
        lastModified: result.fileData.lastModified,
        previewImage: result.previewImage,
        analysis: result.analysis
      },
      conversationId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Figma analysis error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    res.status(500).json({
      error: `Failed to analyze Figma design: ${errorMessage}`,
      figmaUrl
    });
  }
}));

/**
 * POST /api/figma/validate
 * Validate a Figma URL without full analysis
 */
router.post('/validate', handleAsync(async (req: Request, res: Response) => {
  const { figmaUrl } = req.body;

  if (!figmaUrl) {
    return res.status(400).json({
      error: 'Missing required field: figmaUrl'
    });
  }

  const isValid = getFigmaService().isValidFigmaUrl(figmaUrl);
  const fileKey = getFigmaService().extractFileKey(figmaUrl);

  res.json({
    valid: isValid,
    fileKey,
    url: figmaUrl
  });
}));

/**
 * Generate a natural language response for Figma analysis
 */
function generateFigmaAnalysisResponse(analysis: any, fileName: string): string {
  const complexityEmoji = {
    low: '🟢',
    medium: '🟡', 
    high: '🔴'
  };

  let response = `I've analyzed your Figma design "${fileName}" and here's what I found:\n\n`;

  // Design summary
  response += `**Design Overview:**\n${analysis.designSummary}\n\n`;

  // Complexity assessment
  response += `**Complexity Level:** ${complexityEmoji[analysis.estimatedComplexity]} ${analysis.estimatedComplexity.toUpperCase()}\n\n`;

  // Colors
  if (analysis.colors.length > 0) {
    response += `**Color Palette:**\n${analysis.colors.map((color: string) => `• ${color}`).join('\n')}\n\n`;
  }

  // Typography
  if (analysis.typography.length > 0) {
    response += `**Typography:**\n${analysis.typography.map((font: string) => `• ${font}`).join('\n')}\n\n`;
  }

  // Components
  if (analysis.components.length > 0) {
    response += `**Key Components:**\n${analysis.components.map((comp: string) => `• ${comp}`).join('\n')}\n\n`;
  }

  // Layout structure
  response += `**Layout Structure:**\n${analysis.layoutStructure}\n\n`;

  // Technical recommendations
  if (analysis.technicalRecommendations.length > 0) {
    response += `**Technical Recommendations:**\n${analysis.technicalRecommendations.map((rec: string) => `• ${rec}`).join('\n')}\n\n`;
  }

  // Call to action that integrates with existing conversation flow
  response += `Based on this Figma analysis, I can provide detailed development estimates and technical specifications.\n\n`;
  
  // Add assumptions that trigger the existing assumption system
  response += `**Development Assumptions:**\n`;
  response += `Assume: You want a responsive web application that matches this design exactly\n`;
  response += `Assume: You need modern browser compatibility and mobile optimization\n`;
  response += `Assume: You want component-based architecture for maintainability\n`;
  response += `Assume: You need user authentication and data persistence features\n\n`;
  
  response += `Would you like me to break down the implementation approach or estimate development time for specific features?`;

  return response;
}

export default router;