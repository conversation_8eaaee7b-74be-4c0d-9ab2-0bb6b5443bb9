import { OpenAI } from 'openai';

export interface FigmaFileResponse {
  name: string;
  lastModified: string;
  thumbnailUrl: string;
  document: {
    id: string;
    name: string;
    type: string;
    children: any[];
  };
}

export interface FigmaImageResponse {
  images: Record<string, string>;
}

export interface FigmaAnalysis {
  designSummary: string;
  colors: string[];
  typography: string[];
  components: string[];
  layoutStructure: string;
  estimatedComplexity: 'low' | 'medium' | 'high';
  technicalRecommendations: string[];
}

export class FigmaService {
  private readonly figmaToken: string;
  private readonly openai: OpenAI;
  private readonly baseUrl = 'https://api.figma.com/v1';

  constructor() {
    this.figmaToken = process.env.FIGMA_ACCESS_TOKEN || '';
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY!,
    });

    if (!this.figmaToken) {
      console.warn('Warning: FIGMA_ACCESS_TOKEN not set in environment variables');
    }
  }

  /**
   * Extract Figma file key from various URL formats
   */
  extractFileKey(figmaUrl: string): string | null {
    const patterns = [
      /figma\.com\/file\/([a-zA-Z0-9]+)/,
      /figma\.com\/design\/([a-zA-Z0-9]+)/,
      /figma\.com\/proto\/([a-zA-Z0-9]+)/,
    ];

    for (const pattern of patterns) {
      const match = figmaUrl.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return null;
  }

  /**
   * Validate Figma URL format
   */
  isValidFigmaUrl(url: string): boolean {
    try {
      new URL(url);
      return url.includes('figma.com') && this.extractFileKey(url) !== null;
    } catch {
      return false;
    }
  }

  /**
   * Fetch Figma file metadata
   */
  async fetchFileMetadata(fileKey: string): Promise<FigmaFileResponse> {
    if (!this.figmaToken) {
      throw new Error('Figma access token not configured');
    }

    const response = await fetch(`${this.baseUrl}/files/${fileKey}`, {
      headers: {
        'X-Figma-Token': this.figmaToken,
      },
    });

    if (!response.ok) {
      if (response.status === 403) {
        throw new Error('Access denied. Please check your Figma file permissions or access token.');
      }
      if (response.status === 404) {
        throw new Error('Figma file not found. Please check the URL.');
      }
      throw new Error(`Failed to fetch Figma file: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  }

  /**
   * Get image exports of Figma file
   */
  async fetchFileImages(fileKey: string, nodeIds?: string[]): Promise<FigmaImageResponse> {
    if (!this.figmaToken) {
      throw new Error('Figma access token not configured');
    }

    let url = `${this.baseUrl}/images/${fileKey}?format=png&scale=2`;
    
    if (nodeIds && nodeIds.length > 0) {
      url += `&ids=${nodeIds.join(',')}`;
    }

    const response = await fetch(url, {
      headers: {
        'X-Figma-Token': this.figmaToken,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch Figma images: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  }

  /**
   * Extract design elements from Figma document structure
   */
  private extractDesignElements(document: any): {
    colors: Set<string>;
    textStyles: Set<string>;
    componentNames: Set<string>;
  } {
    const colors = new Set<string>();
    const textStyles = new Set<string>();
    const componentNames = new Set<string>();

    const traverse = (node: any) => {
      // Extract colors from fills and strokes
      if (node.fills) {
        node.fills.forEach((fill: any) => {
          if (fill.type === 'SOLID' && fill.color) {
            const { r, g, b } = fill.color;
            colors.add(`rgb(${Math.round(r * 255)}, ${Math.round(g * 255)}, ${Math.round(b * 255)})`);
          }
        });
      }

      if (node.strokes) {
        node.strokes.forEach((stroke: any) => {
          if (stroke.color) {
            const { r, g, b } = stroke.color;
            colors.add(`rgb(${Math.round(r * 255)}, ${Math.round(g * 255)}, ${Math.round(b * 255)})`);
          }
        });
      }

      // Extract text styles
      if (node.style && node.type === 'TEXT') {
        const style = `${node.style.fontFamily || 'Unknown'} ${node.style.fontSize || 'Unknown'}px`;
        textStyles.add(style);
      }

      // Extract component names
      if (node.type === 'COMPONENT' || node.type === 'COMPONENT_SET') {
        componentNames.add(node.name);
      }

      // Recursively process children
      if (node.children) {
        node.children.forEach((child: any) => traverse(child));
      }
    };

    traverse(document);
    return { colors, textStyles, componentNames };
  }

  /**
   * Analyze Figma design using AI
   */
  async analyzeFigmaDesign(fileData: FigmaFileResponse, imageUrl?: string): Promise<FigmaAnalysis> {
    const { colors, textStyles, componentNames } = this.extractDesignElements(fileData.document);

    // Create analysis prompt
    const designInfo = {
      fileName: fileData.name,
      lastModified: fileData.lastModified,
      extractedColors: Array.from(colors).slice(0, 10), // Limit to prevent token overflow
      extractedFonts: Array.from(textStyles).slice(0, 10),
      componentCount: componentNames.size,
      componentNames: Array.from(componentNames).slice(0, 20),
    };

    const systemPrompt = `You are a design analysis expert. Analyze the provided Figma design data and provide a comprehensive assessment for software development estimation.

Focus on:
1. Overall design complexity and structure
2. Technical implementation requirements
3. Development time estimates
4. Technology recommendations
5. Potential challenges

Provide your analysis in a structured format that helps with project estimation.`;

    const userPrompt = `Analyze this Figma design:

File: ${designInfo.fileName}
Last Modified: ${designInfo.lastModified}
Colors Found: ${designInfo.extractedColors.join(', ')}
Typography: ${designInfo.extractedFonts.join(', ')}
Components: ${designInfo.componentCount} total (${designInfo.componentNames.join(', ')})

${imageUrl ? `Visual Preview Available: ${imageUrl}` : 'No visual preview available'}

Provide:
1. Design summary (2-3 sentences)
2. Key colors (hex codes if possible)
3. Typography requirements
4. Component complexity assessment
5. Layout structure description
6. Estimated complexity (low/medium/high)
7. Technical recommendations for implementation

Format as JSON with these exact keys: designSummary, colors, typography, components, layoutStructure, estimatedComplexity, technicalRecommendations`;

    const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ];

    // Add image if available
    if (imageUrl) {
      messages.push({
        role: 'user',
        content: [
          { type: 'text', text: 'Here is the visual preview of the design:' },
          { type: 'image_url', image_url: { url: imageUrl } }
        ]
      });
    }

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4-vision-preview',
        messages,
        max_tokens: 1500,
        temperature: 0.3,
      });

      const analysisText = response.choices[0]?.message?.content || '';

      // Try to parse JSON response
      try {
        const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const analysis = JSON.parse(jsonMatch[0]);
          return {
            designSummary: analysis.designSummary || 'Design analysis completed',
            colors: Array.isArray(analysis.colors) ? analysis.colors : [],
            typography: Array.isArray(analysis.typography) ? analysis.typography : [],
            components: Array.isArray(analysis.components) ? analysis.components : [],
            layoutStructure: analysis.layoutStructure || 'Standard web layout',
            estimatedComplexity: ['low', 'medium', 'high'].includes(analysis.estimatedComplexity) 
              ? analysis.estimatedComplexity 
              : 'medium',
            technicalRecommendations: Array.isArray(analysis.technicalRecommendations) 
              ? analysis.technicalRecommendations 
              : [],
          };
        }
      } catch (parseError) {
        // Fallback to text parsing
        console.log('JSON parsing failed, using fallback analysis');
      }

      // Fallback analysis
      return {
        designSummary: analysisText.slice(0, 200) + '...',
        colors: Array.from(colors).slice(0, 5),
        typography: Array.from(textStyles).slice(0, 3),
        components: Array.from(componentNames).slice(0, 5),
        layoutStructure: 'Complex multi-component layout',
        estimatedComplexity: componentNames.size > 20 ? 'high' : componentNames.size > 10 ? 'medium' : 'low',
        technicalRecommendations: ['React components', 'CSS modules', 'Responsive design'],
      };

    } catch (error) {
      console.error('AI analysis failed:', error);
      throw new Error('Failed to analyze design with AI');
    }
  }

  /**
   * Complete Figma analysis workflow
   */
  async processFigmaUrl(figmaUrl: string): Promise<{
    fileData: FigmaFileResponse;
    analysis: FigmaAnalysis;
    previewImage?: string;
  }> {
    // Validate URL
    if (!this.isValidFigmaUrl(figmaUrl)) {
      throw new Error('Invalid Figma URL format');
    }

    // Extract file key
    const fileKey = this.extractFileKey(figmaUrl);
    if (!fileKey) {
      throw new Error('Could not extract file key from URL');
    }

    // Fetch file metadata
    const fileData = await this.fetchFileMetadata(fileKey);

    // Try to get preview image
    let previewImage: string | undefined;
    try {
      const imageResponse = await this.fetchFileImages(fileKey);
      const imageUrls = Object.values(imageResponse.images);
      if (imageUrls.length > 0) {
        previewImage = imageUrls[0];
      }
    } catch (imageError) {
      console.warn('Could not fetch preview image:', imageError);
    }

    // Analyze design
    const analysis = await this.analyzeFigmaDesign(fileData, previewImage);

    return {
      fileData,
      analysis,
      previewImage,
    };
  }
}