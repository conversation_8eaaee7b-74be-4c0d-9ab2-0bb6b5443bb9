import OpenAI from 'openai';
import { analyzeConversationState, generateSystemPrompt } from '../prompts/index.js';
import { ConversationService } from './conversationService.js';
import { StorageService } from './storageService.js';

// Initialize OpenAI lazily
let openai: OpenAI | null = null;

function getOpenAI(): OpenAI {
  if (!openai) {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }
  return openai;
}

export class ChatService {
  static async processChat(messages: any[], format: string | undefined, conversationId?: string, assumptionMode: boolean = false) {
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      throw new Error('Messages array is required');
    }

    // Get or create conversation history
    const currentConversationId = conversationId || ConversationService.generateConversationId();
    let conversationHistory = ConversationService.getConversation(currentConversationId);
    
    // Add new user message to conversation history
    const newUserMessage = messages[messages.length - 1];
    conversationHistory = ConversationService.addMessage(currentConversationId, newUserMessage);
    
    // Analyze conversation state using full history
    const conversationState = analyzeConversationState(conversationHistory, assumptionMode);
    const systemPrompt = generateSystemPrompt(format || 'web', conversationState, conversationHistory);

    console.log('Conversation Analysis:', {
      conversationId: currentConversationId,
      messageCount: conversationHistory.length,
      isVague: conversationState.isVague,
      hasEnoughInfo: conversationState.hasEnoughInfo,
      hasComprehensiveResponse: conversationState.hasComprehensiveResponse,
      userFrustration: conversationState.userFrustration,
      questionCount: conversationState.questionCount,
      shouldShowEndPrompt: conversationState.shouldShowEndPrompt,
      shouldAutoGenerateQuote: conversationState.shouldAutoGenerateQuote,
      userResponseCount: conversationState.userResponseCount
    });

    console.log('System Prompt Type:',
      systemPrompt.includes('END_PROMPT_SYSTEM_MESSAGE') ? 'END PROMPT' :
      systemPrompt.includes('COMPREHENSIVE_SOLUTION_PROMPT') ? 'COMPREHENSIVE' :
      systemPrompt.includes('CONTINUE_QUESTIONING_PROMPT') ? 'CONTINUE QUESTIONING' :
      systemPrompt.includes('VAGUE_REQUEST_PROMPT') ? 'VAGUE REQUEST' : 'OTHER'
    );

    // Build the full message history for OpenAI
    const openaiMessages = [
      { role: 'system', content: systemPrompt },
      ...conversationHistory
    ];

    const completion = await getOpenAI().chat.completions.create({
      model: "gpt-4o",
      messages: openaiMessages,
      max_completion_tokens: 1500, // Reduced for faster responses
      // temperature: 0.7,
    });

    const assistantResponse = completion.choices[0].message.content;

    console.log('OpenAI Response:', {
      response: assistantResponse,
      responseLength: assistantResponse?.length || 0,
      isEmpty: !assistantResponse || assistantResponse.trim() === '',
      usage: completion.usage
    });

    // Add assistant response to conversation history
    ConversationService.addMessage(currentConversationId, { role: 'assistant', content: assistantResponse });

    // Save chat session
    await StorageService.saveChatSession(ConversationService.getConversation(currentConversationId), format);

    // Quote generation disabled - AI will provide estimates in text response
    let quoteData = null;

    return {
      response: assistantResponse,
      usage: completion.usage,
      conversationId: currentConversationId,
      shouldShowEndPrompt: conversationState.shouldShowEndPrompt,
      shouldAutoGenerateQuote: conversationState.shouldAutoGenerateQuote,
      quoteData: quoteData,
      userResponseCount: conversationState.userResponseCount
    };
  }

  static async processChatStream(messages: any[], format: string | undefined, conversationId?: string, assumptionMode: boolean = false) {
    console.log('Starting streaming chat processing...');

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      throw new Error('Messages array is required');
    }

    // Get or create conversation history
    const currentConversationId = conversationId || ConversationService.generateConversationId();
    let conversationHistory = ConversationService.getConversation(currentConversationId);

    // Add new user message to conversation history
    const newUserMessage = messages[messages.length - 1];
    conversationHistory = ConversationService.addMessage(currentConversationId, newUserMessage);

    // Analyze conversation state using full history
    const conversationState = analyzeConversationState(conversationHistory, assumptionMode);
    const systemPrompt = generateSystemPrompt(format || 'web', conversationState, conversationHistory);

    console.log('Streaming Conversation Analysis:', {
      conversationId: currentConversationId,
      messageCount: conversationHistory.length,
      isVague: conversationState.isVague,
      hasEnoughInfo: conversationState.hasEnoughInfo,
      hasComprehensiveResponse: conversationState.hasComprehensiveResponse,
      userFrustration: conversationState.userFrustration,
      questionCount: conversationState.questionCount
    });

    // Build the full message history for OpenAI
    const openaiMessages = [
      { role: 'system', content: systemPrompt },
      ...conversationHistory
    ];

    // Use regular API call instead of streaming to avoid organization verification requirement
    const completion = await getOpenAI().chat.completions.create({
      model: "gpt-4o",
      messages: openaiMessages,
      max_tokens: 1500,
      stream: false,
      // temperature: 0.7,
    });

    // Extract the response content
    const responseContent = completion.choices[0]?.message?.content || '';

    return {
      response: responseContent,
      conversationId: currentConversationId,
      conversationState,
      conversationHistory,
      format: format || 'web'
    };
  }

  static async processFollowup(messages: any[], conversationId: string, assumptionMode: boolean = false) {
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      throw new Error('Messages array is required');
    }

    // Get existing conversation history
    let conversationHistory = ConversationService.getConversation(conversationId);
    
    // Add new user message to conversation history
    const newUserMessage = messages[messages.length - 1];
    conversationHistory = ConversationService.addMessage(conversationId, newUserMessage);

    // For follow-up questions, analyze the conversation state using full history
    const conversationState = analyzeConversationState(conversationHistory, assumptionMode);
    const systemPrompt = generateSystemPrompt('web', conversationState, conversationHistory);

    console.log('Follow-up Analysis:', {
      conversationId,
      messageCount: conversationHistory.length,
      hasComprehensiveResponse: conversationState.hasComprehensiveResponse,
      userFrustration: conversationState.userFrustration
    });

    // Build the full message history for OpenAI
    const openaiMessages = [
      { role: 'system', content: systemPrompt },
      ...conversationHistory
    ];

    const completion = await getOpenAI().chat.completions.create({
      model: "gpt-4o",
      messages: openaiMessages,
      max_completion_tokens: 2000,
      // temperature: 0.7,
    });

    const assistantResponse = completion.choices[0].message.content;
    
    // Add assistant response to conversation history
    ConversationService.addMessage(conversationId, { role: 'assistant', content: assistantResponse });

    // Quote generation disabled - AI will provide estimates in text response
    let quoteData = null;

    return {
      response: assistantResponse,
      usage: completion.usage,
      conversationId: conversationId,
      shouldShowEndPrompt: conversationState.shouldShowEndPrompt,
      shouldAutoGenerateQuote: conversationState.shouldAutoGenerateQuote,
      quoteData: quoteData,
      userResponseCount: conversationState.userResponseCount
    };
  }

  static async processFollowupStream(messages: any[], conversationId: string, assumptionMode: boolean = false) {
    console.log('Starting streaming follow-up processing...');

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      throw new Error('Messages array is required');
    }

    if (!conversationId) {
      throw new Error('Conversation ID is required for follow-up');
    }

    // Get existing conversation history
    let conversationHistory = ConversationService.getConversation(conversationId);
    if (!conversationHistory || conversationHistory.length === 0) {
      throw new Error('Conversation not found');
    }

    // Add new user message to conversation history
    const newUserMessage = messages[messages.length - 1];
    conversationHistory = ConversationService.addMessage(conversationId, newUserMessage);

    // Analyze conversation state using full history
    const conversationState = analyzeConversationState(conversationHistory, assumptionMode);
    const systemPrompt = generateSystemPrompt('web', conversationState, conversationHistory);

    console.log('Streaming Follow-up Analysis:', {
      conversationId,
      messageCount: conversationHistory.length,
      hasComprehensiveResponse: conversationState.hasComprehensiveResponse,
      userFrustration: conversationState.userFrustration
    });

    // Build the full message history for OpenAI
    const openaiMessages = [
      { role: 'system', content: systemPrompt },
      ...conversationHistory
    ];

    // Use regular API call instead of streaming to avoid organization verification requirement
    const completion = await getOpenAI().chat.completions.create({
      model: "gpt-4o",
      messages: openaiMessages,
      max_tokens: 1500,
      stream: false,
      // temperature: 0.7,
    });

    // Extract the response content
    const responseContent = completion.choices[0]?.message?.content || '';

    return {
      response: responseContent,
      conversationId,
      conversationState,
      conversationHistory
    };
  }

  static async processImageAnalysis(message: string, format: string | undefined, base64Image: string, mimeType: string) {
    // Create initial conversation for image analysis
    const conversationId = ConversationService.generateConversationId();
    const initialMessage = { role: 'user', content: message || "Please analyze this design and help me estimate the project." };
    
    // Apply the same questioning approach for image uploads
    const conversationState = analyzeConversationState([initialMessage]);
    const systemPrompt = generateSystemPrompt(format || 'web', conversationState, [initialMessage]);

    const completion = await getOpenAI().chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: message || "Please analyze this design and help me estimate the project."
            },
            {
              type: "image_url",
              image_url: {
                url: `data:${mimeType};base64,${base64Image}`
              }
            }
          ]
        }
      ],
      max_completion_tokens: 2000,
      // temperature: 0.7,
    });

    const assistantResponse = completion.choices[0].message.content;
    
    // Store conversation history
    const conversationHistory = [
      initialMessage,
      { role: 'assistant', content: assistantResponse }
    ];
    ConversationService.setConversation(conversationId, conversationHistory);

    return {
      response: assistantResponse,
      usage: completion.usage,
      imageAnalyzed: true,
      conversationId: conversationId
    };
  }
}

// Quote generation function (simplified version of frontend logic)
export function generateProjectQuote(conversationHistory: any[], format: string) {
  // Extract project description from conversation
  const userMessages = conversationHistory.filter(msg => msg.role === 'user');
  const projectDescription = userMessages.map(msg => msg.content).join(' ');
  
  // Analyze project complexity
  const complexity = analyzeComplexity(projectDescription);
  const projectType = analyzeProjectType(projectDescription, format);
  const riskFactor = analyzeRiskFactor(projectDescription, format);
  const hourlyRate = 150;

  // Base estimates
  const baseEstimates = getBaseEstimates(complexity, format);
  
  // Calculate traditional costs
  const traditionalTotalHours = baseEstimates.developmentHours + baseEstimates.projectManagementHours + 
                               baseEstimates.designHours + baseEstimates.qaHours + 
                               baseEstimates.infrastructureHours + baseEstimates.maintenanceHours;
  
  const traditional = {
    ...baseEstimates,
    hourlyRate,
    complexity,
    projectType,
    riskFactor,
    total: Math.round(traditionalTotalHours * hourlyRate * (1 + riskFactor)),
    breakdown: {
      development: Math.round(baseEstimates.developmentHours * hourlyRate * (1 + riskFactor)),
      projectManagement: Math.round(baseEstimates.projectManagementHours * hourlyRate * (1 + riskFactor)),
      design: Math.round(baseEstimates.designHours * hourlyRate * (1 + riskFactor)),
      qa: Math.round(baseEstimates.qaHours * hourlyRate * (1 + riskFactor)),
      infrastructure: Math.round(baseEstimates.infrastructureHours * hourlyRate * (1 + riskFactor)),
      maintenance: Math.round(baseEstimates.maintenanceHours * hourlyRate * (1 + riskFactor))
    }
  };

  // Calculate AI-driven savings
  const savingsPercentage = complexity === 'large' ? 0.25 : 0.40;
  const aiEfficiencyFactors = {
    development: savingsPercentage,
    projectManagement: savingsPercentage * 0.5,
    design: savingsPercentage * 0.3,
    qa: savingsPercentage * 0.6,
    infrastructure: savingsPercentage * 0.4,
    maintenance: savingsPercentage * 0.7
  };

  const aiDriven = {
    developmentHours: Math.round(baseEstimates.developmentHours * (1 - aiEfficiencyFactors.development)),
    projectManagementHours: Math.round(baseEstimates.projectManagementHours * (1 - aiEfficiencyFactors.projectManagement)),
    designHours: Math.round(baseEstimates.designHours * (1 - aiEfficiencyFactors.design)),
    qaHours: Math.round(baseEstimates.qaHours * (1 - aiEfficiencyFactors.qa)),
    infrastructureHours: Math.round(baseEstimates.infrastructureHours * (1 - aiEfficiencyFactors.infrastructure)),
    maintenanceHours: Math.round(baseEstimates.maintenanceHours * (1 - aiEfficiencyFactors.maintenance)),
    hourlyRate,
    complexity,
    projectType,
    riskFactor: riskFactor * 0.6,
    total: 0,
    breakdown: {
      development: 0,
      projectManagement: 0,
      design: 0,
      qa: 0,
      infrastructure: 0,
      maintenance: 0
    }
  };

  // Calculate AI-driven breakdown
  aiDriven.breakdown.development = Math.round(aiDriven.developmentHours * hourlyRate * (1 + aiDriven.riskFactor));
  aiDriven.breakdown.projectManagement = Math.round(aiDriven.projectManagementHours * hourlyRate * (1 + aiDriven.riskFactor));
  aiDriven.breakdown.design = Math.round(aiDriven.designHours * hourlyRate * (1 + aiDriven.riskFactor));
  aiDriven.breakdown.qa = Math.round(aiDriven.qaHours * hourlyRate * (1 + aiDriven.riskFactor));
  aiDriven.breakdown.infrastructure = Math.round(aiDriven.infrastructureHours * hourlyRate * (1 + aiDriven.riskFactor));
  aiDriven.breakdown.maintenance = Math.round(aiDriven.maintenanceHours * hourlyRate * (1 + aiDriven.riskFactor));
  
  aiDriven.total = Object.values(aiDriven.breakdown).reduce((sum, cost) => sum + cost, 0);

  // Ensure no quote exceeds $85,000
  if (traditional.total > 85000) {
    const scaleFactor = 85000 / traditional.total;
    
    Object.keys(traditional.breakdown).forEach(key => {
      traditional.breakdown[key as keyof typeof traditional.breakdown] = Math.round(traditional.breakdown[key as keyof typeof traditional.breakdown] * scaleFactor);
    });
    traditional.total = 85000;
    
    const newAiTotal = Math.round(traditional.total * (1 - savingsPercentage));
    const aiScaleFactor = newAiTotal / aiDriven.total;
    
    Object.keys(aiDriven.breakdown).forEach(key => {
      aiDriven.breakdown[key as keyof typeof aiDriven.breakdown] = Math.round(aiDriven.breakdown[key as keyof typeof aiDriven.breakdown] * aiScaleFactor);
    });
    aiDriven.total = newAiTotal;
  }

  const savings = {
    amount: traditional.total - aiDriven.total,
    percentage: Math.round(((traditional.total - aiDriven.total) / traditional.total) * 100)
  };

  const timeline = {
    traditional: Math.ceil(traditionalTotalHours / 40),
    aiDriven: Math.ceil((aiDriven.developmentHours + aiDriven.projectManagementHours + 
                        aiDriven.designHours + aiDriven.qaHours + 
                        aiDriven.infrastructureHours + aiDriven.maintenanceHours) / 40)
  };

  const confidence = calculateConfidence(complexity, projectType, riskFactor);

  return { 
    traditional, 
    aiDriven, 
    savings, 
    timeline, 
    confidence
  } as any;
}

function analyzeComplexity(description: string): 'small' | 'medium' | 'large' {
  const complexityIndicators = {
    small: ['simple', 'basic', 'minimal', 'single page', 'landing page', 'portfolio', 'blog'],
    medium: ['dashboard', 'user management', 'database', 'api', 'authentication', 'e-commerce', 'cms'],
    large: ['enterprise', 'complex', 'multiple systems', 'integration', 'scalable', 'microservices', 'real-time', 'blockchain']
  };

  const lowerDesc = description.toLowerCase();
  
  if (complexityIndicators.large.some(indicator => lowerDesc.includes(indicator))) {
    return 'large';
  }
  if (complexityIndicators.medium.some(indicator => lowerDesc.includes(indicator))) {
    return 'medium';
  }
  return 'small';
}

function analyzeProjectType(description: string, format: string): string {
  const typeIndicators = {
    'E-commerce': ['shop', 'store', 'cart', 'payment', 'checkout', 'product'],
    'SaaS Platform': ['subscription', 'tenant', 'billing', 'analytics', 'dashboard'],
    'Mobile App': ['mobile', 'ios', 'android', 'app store'],
    'Enterprise System': ['enterprise', 'erp', 'crm', 'workflow', 'automation'],
    'Content Management': ['cms', 'blog', 'content', 'publishing', 'editorial'],
    'Real-time Application': ['real-time', 'chat', 'messaging', 'live', 'streaming'],
    'API/Backend': ['api', 'backend', 'microservice', 'rest', 'graphql'],
    'Web Application': ['web', 'webapp', 'browser', 'responsive']
  };

  const lowerDesc = description.toLowerCase();
  
  for (const [type, indicators] of Object.entries(typeIndicators)) {
    if (indicators.some(indicator => lowerDesc.includes(indicator))) {
      return type;
    }
  }
  
  return format.charAt(0).toUpperCase() + format.slice(1).replace('-', ' ');
}

function analyzeRiskFactor(description: string, format: string): number {
  let riskFactor = 0.1;
  
  const highRiskIndicators = ['integration', 'legacy', 'migration', 'complex', 'real-time', 'blockchain'];
  const mediumRiskIndicators = ['authentication', 'payment', 'security', 'scalable', 'performance'];
  const lowRiskIndicators = ['simple', 'basic', 'standard', 'template'];
  
  const lowerDesc = description.toLowerCase();
  
  if (highRiskIndicators.some(indicator => lowerDesc.includes(indicator))) {
    riskFactor += 0.2;
  }
  if (mediumRiskIndicators.some(indicator => lowerDesc.includes(indicator))) {
    riskFactor += 0.1;
  }
  if (lowRiskIndicators.some(indicator => lowerDesc.includes(indicator))) {
    riskFactor -= 0.05;
  }
  
  return Math.max(0.05, Math.min(0.3, riskFactor));
}

function calculateConfidence(complexity: string, projectType: string, riskFactor: number): number {
  let confidence = 85;
  
  if (complexity === 'small') confidence += 10;
  else if (complexity === 'large') confidence -= 10;
  
  confidence -= Math.round(riskFactor * 100);
  
  const familiarTypes = ['Web Application', 'API/Backend', 'Content Management'];
  if (familiarTypes.includes(projectType)) confidence += 5;
  
  return Math.max(70, Math.min(95, confidence));
}

function getBaseEstimates(complexity: 'small' | 'medium' | 'large', format: string) {
  // Adjusted base hours to align with 6 weeks to 2 months timeline for MVP
  const baseHours = {
    small: { dev: 120, pm: 30, design: 40, qa: 30, infra: 20, maintenance: 15 },    // ~6-7 weeks
    medium: { dev: 240, pm: 60, design: 80, qa: 60, infra: 40, maintenance: 30 },  // ~7-8 weeks
    large: { dev: 320, pm: 80, design: 100, qa: 80, infra: 50, maintenance: 40 }   // ~8-10 weeks
  };

  const formatMultipliers: { [key: string]: number } = {
    'web': 1.0,
    'webapp': 1.1,
    'backend-api': 0.8,
    'database-integration': 1.4,
    'model-training': 1.6,
    'hybrid-webapp': 1.3,
    'hybrid-platform': 1.4,
    'microservices': 1.5,
    'cloud-native': 1.3,
    'real-time-app': 1.4,
    'blockchain-app': 1.8,
    'iot-integration': 1.6,
    'ai-ml-integration': 1.7,
    'enterprise-system': 1.5
  };

  const multiplier = formatMultipliers[format] || 1.0;
  const base = baseHours[complexity];

  return {
    developmentHours: Math.round(base.dev * multiplier),
    projectManagementHours: Math.round(base.pm * multiplier),
    designHours: Math.round(base.design * multiplier),
    qaHours: Math.round(base.qa * multiplier),
    infrastructureHours: Math.round(base.infra * multiplier),
    maintenanceHours: Math.round(base.maintenance * multiplier)
  };
}
